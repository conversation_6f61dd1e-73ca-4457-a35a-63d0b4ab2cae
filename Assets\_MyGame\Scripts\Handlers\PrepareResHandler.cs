using FairyGUI;
using UnityEngine;
using static GameConfig;

public class PrepareResHandler : HandlerBase
{
    public override void Enter()
    {
        // LoadingPanel.Show();
        step = Step.InitFont;
    }

    enum Step
    {
        Empty,
        InitFont,
        LoadUIPackage,
        LoadConfig,
        EnterLobby
    }
    Step step;

    private float StartProgressValue = 0f;
    public override void Update()
    {
        if (step == Step.Empty)
            return;

        if (step == Step.InitFont)
        {
            step = Step.Empty;
            SetProgress(StartProgressValue + 0.1f);
            InitFont();
        }
        else if (step == Step.LoadUIPackage)
        {
            step = Step.Empty;
            SetProgress(StartProgressValue + 0.2f);
            LoadUIPackage();
        }
        else if (step == Step.LoadConfig)
        {
            step = Step.Empty;
            SetProgress(StartProgressValue + 0.4f);
            LoadConfig();
        }
        else if (step == Step.EnterLobby)
        {
            step = Step.Empty;
            SetProgress(1f);
            EnterLobby();
        }
    }

    private void SetProgress(float value)
    {
        LoadingPanel.SetProgress(value);
    }

    private void InitFont()
    {
#if WXGAME && !UNITY_EDITOR
        try
        {
            var wxFont = "WxFont";
            var fallbackFont = GameConfig.CdnBase + "WebGL/ALBBPHT-ExtraBold.ttf";
            WeChatWASM.WX.GetWXFont(fallbackFont, (fontRes) =>
            {
                if (fontRes != null)
                {
                    DynamicFont font = new DynamicFont();
                    font.name = wxFont;
                    font.nativeFont = fontRes;
                    FontManager.RegisterFont(font);
                    LoadFontFromAssetBundle(wxFont);
                }
                else
                {
                    LoadFontFromAssetBundle(null);
                }
            });
        }
        catch (System.Exception)
        {
            LoadFontFromAssetBundle(null);
        }
#else
        LoadFontFromAssetBundle(null);
#endif
    }

    private void LoadFontFromAssetBundle(string wxFontName)
    {
        AssetBundleManager.LoadObjects<Font>("Fonts/ALBBPHT-ExtraBold", (objs) =>
        {
            if (objs != null)
            {
                for (int i = 0; i < objs.Length; i++)
                {
                    var fontRes = objs[i] as Font;
                    if (fontRes == null)
                        continue;
                    if (fontRes.name == wxFontName)
                        continue;
                    DynamicFont font = new DynamicFont();
                    font.name = fontRes.name;
                    font.nativeFont = fontRes;
                    FontManager.RegisterFont(font);
                }
            }
            TDAnalyticsSdk.ReportActive(TDAnalyticsSdk.ActiveStep.LoadedFont);
            step = Step.LoadUIPackage;
        });
    }

    private void LoadUIPackage()
    {
        // 包到受保护白名单，防止自动卸载
        FUILoader.AddProtectedPackage("Tips");
        FUILoader.AddProtectedPackage("Icon");
        FUILoader.AddProtectedPackage("Component");
        FUILoader.AddProtectedPackage("Effect");
        FUILoader.AddProtectedPackage("Common");
        FUILoader.AddProtectedPackage("BorderEffect");
        FUILoader.AddProtectedPackage("GuildIcon");
        FUILoader.AddProtectedPackage("HeroIcon");

#if DYGAME
        //补充完整字体
        AssetBundleManager.LoadFont("FontsFull/ALBBPHT-ExtraBold", (fontRes) =>
        {
            if (fontRes != null)
            {
                DynamicFont font = new DynamicFont();
                font.name = fontRes.name;
                font.nativeFont = fontRes;
                FontManager.RegisterFont(font);

                //用默认字体替换微信系统字体
                font = new DynamicFont();
                font.name = "WxFont";
                font.nativeFont = fontRes;
                FontManager.RegisterFont(font);
            }
        });
#endif

        //添加通用包，不建议再添加其他包
        FUILoader.LoadPackage("Component", doneAction: () =>
        {
            TDAnalyticsSdk.ReportActive(TDAnalyticsSdk.ActiveStep.LoadedUIPackage);
            step = Step.LoadConfig;
        });
    }


    private void LoadConfig()
    {
        var configDir = "Data/";
        ConfigLoader configLoader = new ConfigLoader();
        var tables = TableSO.tables;
        for (int i = 0; i < tables.Length; i++)
        {
            var tableInfo = tables[i];
            configLoader.Add(tableInfo);
        }

        configLoader.Add<ConfigEquip, InfoEquip>(configDir + "Equip", "Equip");
        configLoader.Add<ConfigGateTemplate, InfoGateTemplate>(configDir + "GateTemplate", "GateTemplate");
        configLoader.Add<ConfigMonster, InfoMonster>(configDir + "Monster", "Monster");
        configLoader.Add<ConfigWeapon, InfoWeapon>(configDir + "Weapon", "Weapon");
        configLoader.Add<ConfigBullet, InfoBullet>(configDir + "Bullet", "Bullet");
        configLoader.Add<ConfigLevel, InfoLevel>(configDir + "Level", "Level");
        configLoader.Add<ConfigDropItem, InfoDropItem>(configDir + "DropItem", "DropItem");
        configLoader.Add<ConfigMap, InfoMap>(configDir + "Map", "Map");
        configLoader.Add<ConfigBuff, InfoBuff>(configDir + "Buff", "Buff");
        // configLoader.Add<ConfigPet, InfoPet>(configDir + "Pet", "Pet");
        configLoader.Add<ConfigItem, InfoItem>(configDir + "Item", "Item");
        configLoader.Add<ConfigLang, InfoLang>(configDir + "Langui", "Langui");
        configLoader.Add<ConfigLangSysMsg, InfoLang>(configDir + "LangError", "LangError");
        configLoader.Add<ConfigSkill, InfoSkill>(configDir + "Skill", "Skill");
        configLoader.Add<ConfigEntry, InfoEntry>(configDir + "Entry", "Entry");
        configLoader.Add<ConfigGuide, InfoGuide>(configDir + "Guide", "Guide");
        configLoader.Add<ConfigUserAgreement, InfoUserAgreement>(configDir + "UserAgreement", "UserAgreement");
        configLoader.Add<ConfigShare, InfoShare>(configDir + "Share", "Share");
        configLoader.Add<ConfigResourceGuide, InfoResourceGuide>(configDir + "ResourceGuide", "ResourceGuide");
        configLoader.Add<ConfigGateMonsterShow, InfoGateMonsterShow>(configDir + "GateMonsterShow", "GateMonsterShow");
        configLoader.Add<ConfigHelpTips, InfoHelpTip>(configDir + "HelpTips", "HelpTips");
        configLoader.Add<ConfigBattleGuide, InfoBattleGuide>(configDir + "BattleGuide", "BattleGuide");
        configLoader.Add<ConfigBorderInfo, InfoBorderInfo>(configDir + "BorderInfo", "BorderInfo");
        configLoader.Add<ConfigCardFrame, InfoCardFrame>(configDir + "CardFrame", "CardFrame");
        configLoader.Add<ConfigMidWayMonster, InfoMidWayMonster>(configDir + "MidWayMonsterWave", "MidWayMonsterWave");
        configLoader.Add<ConfigMidWayBuffItem, InfoMidWayBuffItem>(configDir + "MidWayBuffItem", "MidWayBuffItem");
        configLoader.Add<ConfigTreasureRaidersMonster, InfoTreasureRaidersMonster>(configDir + "TreasureRaidersMonsterWava", "TreasureRaidersMonsterWava");
        configLoader.Add<ConfigTreasureRaidersBuffItem, InfoTreasureRaidersBuffItem>(configDir + "TreasureRaidersBuffItem", "TreasureRaidersBuffItem");
        configLoader.Add<ConfigFlyBulletMonster, InfoFlyBulletMonster>(configDir + "FlyBulletMonsterWave", "FlyBulletMonsterWave");
        configLoader.Add<ConfigFlyBulletBuffItem, InfoFlyBulletBuffItem>(configDir + "FlyBulletBuffItem", "FlyBulletBuffItem");
        configLoader.Add<ConfigDragonMonster, InfoDragonMonster>(configDir + "DragonMonster", "DragonMonster");
        configLoader.Add<ConfigDragonBuffItem, InfoDragonBuffItem>(configDir + "DragonBuffItems", "DragonBuffItems");
        configLoader.Add<ConfigDragonGearMonster, InfoDragonGearMonster>(configDir + "DragonGearMonster", "DragonGearMonster");
        configLoader.Add<ConfigDragonGearHero, InfoDragonGearHero>(configDir + "DragonGearHero", "DragonGearHero");
        configLoader.Add<ConfigDragonGearRate, InfoDragonGearRate>(configDir + "DragonGearRate", "DragonGearRate");
        configLoader.Add<ConfigPVPBuff, InfoPVPBuff>(configDir + "hero_battle_buff_effect_config", "hero_battle_buff_effect_config");
        configLoader.Add<ConfigPVPSkill, InfoPVPSkill>(configDir + "hero_battle_skill_config", "hero_battle_skill_config");
        configLoader.Add<ConfigSticker, InfoSticker>(configDir + "ChatSticker", "ChatSticker");
        configLoader.Add<ConfigChatDeco, InfoChatDeco>(configDir + "ChatDeco", "ChatDeco");
        configLoader.Add<ConfigRoleName, InfoRoleName>(configDir + "RoleName", "RoleName");
        configLoader.Load((float radio) =>
        {
            if (radio >= 1)
            {
                TDAnalyticsSdk.ReportActive(TDAnalyticsSdk.ActiveStep.LoadedConfig);
                step = Step.EnterLobby;
            }
        });
    }

    private void InitConfig()
    {
        Session.Vibration = StorageMgr.GetVibration();
        Session.ShowRocker = StorageMgr.GetRocker();
        SoundManager.SetBgmVolume(StorageMgr.GetBgVolume());
        SoundManager.SetEffectVolume(StorageMgr.GetEffectVolume());
        Platform.GetInstance().InitShareData();
    }
    private void EnterLobby()
    {
        InitConfig();

        SplashPanel.Hide();
        GameMain.SwitchHanlder(GameMain.HandlerName.Logoin);
    }
}