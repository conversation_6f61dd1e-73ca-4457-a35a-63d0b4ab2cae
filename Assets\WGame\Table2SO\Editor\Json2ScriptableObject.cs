using System;
using System.IO;
using dnlib.DotNet.MD;
using UnityEditor;
using UnityEngine;

public class Json2ScriptableObject
{
    public static string ExcelDir = "../ExcelUI/Excels";
    [MenuItem("Tools/Tabel2SO", priority = 0)]
    public static void Execute()
    {
        Table2Json();
    }

    private static void Table2Json()
    {
        var excelDir = Path.Join(Application.dataPath, ExcelDir);
        var startInfo = new System.Diagnostics.ProcessStartInfo();
        startInfo.WorkingDirectory = excelDir;
        startInfo.UseShellExecute = false;
        startInfo.RedirectStandardOutput = true;
        startInfo.RedirectStandardError = true;

        if (Application.platform == RuntimePlatform.WindowsEditor)
        {
            string batFilePath = Path.Combine(excelDir, "export.bat");
            if (!File.Exists(batFilePath))
            {
                Debug.LogError("export.bat file not found in " + excelDir);
                return;
            }
            startInfo.FileName = batFilePath;
            startInfo.Arguments = GameConfig.GetVer();
        }
        else if (Application.platform == RuntimePlatform.OSXEditor)
        {
            // Using absolute path for node, as Unity may not have the same PATH as the shell
            string command = "if [ -d 'json' ]; then rm -rf 'json'; fi && mkdir 'json' && /usr/local/bin/node index.js --export";
            startInfo.FileName = "/bin/sh";
            startInfo.Arguments = $"-c \"{command}\"";
        }
        else
        {
            Debug.LogError($"Unsupported editor platform: {Application.platform}");
            return;
        }

        var process = new System.Diagnostics.Process();
        process.StartInfo = startInfo;
        try
        {
            var output = new System.Text.StringBuilder();
            var error = new System.Text.StringBuilder();

            process.OutputDataReceived += (sender, args) => { if (args.Data != null) output.AppendLine(args.Data); };
            process.ErrorDataReceived += (sender, args) => { if (args.Data != null) error.AppendLine(args.Data); };

            process.Start();

            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            process.WaitForExit();

            if (process.ExitCode == 0)
            {
                Debug.Log("Process executed successfully.\nOutput:\n" + output.ToString());
                Json2SO();
            }
            else
            {
                Debug.LogError($"Process exited with code {process.ExitCode}.\nOutput:\n{output.ToString()}\nError:\n{error.ToString()}");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError("Error occurred during process execution: " + ex.Message);
        }
    }

    private static void Json2SO()
    {
        var tables = TableSO.tableNames;
        string dataDirectory = "Assets/_MyGame/Bundles/DataSO";

        // 确保目录存在
        if (!Directory.Exists(dataDirectory))
        {
            Directory.CreateDirectory(dataDirectory);
        }

        for (int i = 0; i < tables.Length; i++)
        {
            var tableName = tables[i];
            string jsonPath = Path.Join(Application.dataPath, $"{ExcelDir}/json", $"{tableName}.json");
            string assetPath = $"{dataDirectory}/{tableName}SO.asset";

            // 删除已存在的文件
            if (File.Exists(assetPath))
            {
                File.Delete(assetPath);
            }

            // 读取JSON文件
            string jsonText = File.ReadAllText(jsonPath);
            // ScriptableObject so;
            if (tableName == "BattleGuide")
            {
                TableSO.BattleGuideSo so = TableSO.CreateInstance<TableSO.BattleGuideSo>();
                so.datas = TableSO.CreateArray<InfoBattleGuide>(LitJson.JsonMapper.ToObject(jsonText));
                AssetDatabase.CreateAsset(so, assetPath);
                EditorUtility.SetDirty(so);
            }
            else
            {
                var so = CreateSOInstance(tableName, jsonText);
                AssetDatabase.CreateAsset(so, assetPath);
                EditorUtility.SetDirty(so);
            }

            // 创建SO资产
            Debug.Log($"生成 SO 文件: {assetPath}");
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        Debug.Log("======所有表格导出完成======");
    }

    private static ScriptableObject CreateSOInstance(string tableName, string jsonText)
    {
        var json = LitJson.JsonMapper.ToObject(jsonText);
        return TableSO.GenerateSO(tableName, json);
    }
}
