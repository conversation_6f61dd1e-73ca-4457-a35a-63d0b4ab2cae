%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: ChatStickerSO
  m_EditorClassIdentifier: Assembly-CSharp::TableSO/ChatStickerSo
  _datas:
  - uniqueKey: 1
    index: 1
    groupID: 1
    id: 1
    name: '[bq01]'
    type: 1
    effectType: 0
    effectID: emoji_01
  - uniqueKey: 2
    index: 2
    groupID: 1
    id: 2
    name: '[bq02]'
    type: 1
    effectType: 0
    effectID: emoji_02
  - uniqueKey: 3
    index: 3
    groupID: 1
    id: 3
    name: '[bq03]'
    type: 1
    effectType: 0
    effectID: emoji_03
  - uniqueKey: 4
    index: 4
    groupID: 1
    id: 4
    name: '[bq04]'
    type: 1
    effectType: 0
    effectID: emoji_04
  - uniqueKey: 5
    index: 5
    groupID: 1
    id: 5
    name: '[bq05]'
    type: 1
    effectType: 0
    effectID: emoji_05
  - uniqueKey: 6
    index: 6
    groupID: 1
    id: 6
    name: '[bq06]'
    type: 1
    effectType: 0
    effectID: emoji_06
  - uniqueKey: 7
    index: 7
    groupID: 1
    id: 7
    name: '[bq07]'
    type: 1
    effectType: 0
    effectID: emoji_07
  - uniqueKey: 8
    index: 8
    groupID: 1
    id: 8
    name: '[bq08]'
    type: 1
    effectType: 0
    effectID: emoji_08
  - uniqueKey: 9
    index: 9
    groupID: 1
    id: 9
    name: '[bq09]'
    type: 1
    effectType: 0
    effectID: emoji_09
  - uniqueKey: 10
    index: 10
    groupID: 1
    id: 10
    name: '[bq10]'
    type: 1
    effectType: 0
    effectID: emoji_10
  - uniqueKey: 11
    index: 11
    groupID: 1
    id: 11
    name: '[bq11]'
    type: 1
    effectType: 0
    effectID: emoji_11
  - uniqueKey: 12
    index: 12
    groupID: 1
    id: 12
    name: '[bq12]'
    type: 1
    effectType: 0
    effectID: emoji_12
  - uniqueKey: 13
    index: 13
    groupID: 1
    id: 13
    name: '[bq13]'
    type: 1
    effectType: 0
    effectID: emoji_13
  - uniqueKey: 14
    index: 14
    groupID: 1
    id: 14
    name: '[bq14]'
    type: 1
    effectType: 0
    effectID: emoji_14
  - uniqueKey: 15
    index: 15
    groupID: 1
    id: 15
    name: '[bq15]'
    type: 1
    effectType: 0
    effectID: emoji_15
  - uniqueKey: 16
    index: 16
    groupID: 1
    id: 16
    name: '[bq16]'
    type: 1
    effectType: 0
    effectID: emoji_16
  - uniqueKey: 17
    index: 17
    groupID: 1
    id: 17
    name: '[bq17]'
    type: 1
    effectType: 0
    effectID: emoji_17
  - uniqueKey: 18
    index: 18
    groupID: 1
    id: 18
    name: '[bq18]'
    type: 1
    effectType: 0
    effectID: emoji_18
  - uniqueKey: 19
    index: 19
    groupID: 1
    id: 19
    name: '[bq19]'
    type: 1
    effectType: 0
    effectID: emoji_19
  - uniqueKey: 20
    index: 20
    groupID: 1
    id: 20
    name: '[bq20]'
    type: 1
    effectType: 0
    effectID: emoji_20
  - uniqueKey: 21
    index: 21
    groupID: 1
    id: 21
    name: '[bq21]'
    type: 1
    effectType: 0
    effectID: emoji_21
  - uniqueKey: 22
    index: 22
    groupID: 1
    id: 22
    name: '[bq22]'
    type: 1
    effectType: 0
    effectID: emoji_22
  - uniqueKey: 23
    index: 23
    groupID: 1
    id: 23
    name: '[bq23]'
    type: 1
    effectType: 0
    effectID: emoji_23
  - uniqueKey: 24
    index: 24
    groupID: 1
    id: 24
    name: '[bq24]'
    type: 1
    effectType: 0
    effectID: emoji_24
  - uniqueKey: 25
    index: 25
    groupID: 1
    id: 25
    name: '[bq25]'
    type: 1
    effectType: 0
    effectID: emoji_25
  - uniqueKey: 26
    index: 26
    groupID: 1
    id: 26
    name: '[bq26]'
    type: 1
    effectType: 0
    effectID: emoji_26
  - uniqueKey: 27
    index: 27
    groupID: 1
    id: 27
    name: '[bq27]'
    type: 1
    effectType: 0
    effectID: emoji_27
  - uniqueKey: 28
    index: 28
    groupID: 1
    id: 28
    name: '[bq28]'
    type: 1
    effectType: 0
    effectID: emoji_28
  - uniqueKey: 29
    index: 29
    groupID: 1
    id: 29
    name: '[bq29]'
    type: 1
    effectType: 0
    effectID: emoji_29
  - uniqueKey: 30
    index: 30
    groupID: 1
    id: 30
    name: '[bq30]'
    type: 1
    effectType: 0
    effectID: emoji_30
  - uniqueKey: 31
    index: 31
    groupID: 1
    id: 31
    name: '[bq31]'
    type: 1
    effectType: 0
    effectID: emoji_31
  - uniqueKey: 32
    index: 32
    groupID: 1
    id: 32
    name: '[bq32]'
    type: 1
    effectType: 0
    effectID: emoji_32
  - uniqueKey: 33
    index: 33
    groupID: 1
    id: 33
    name: '[bq33]'
    type: 1
    effectType: 0
    effectID: emoji_33
  - uniqueKey: 34
    index: 34
    groupID: 1
    id: 34
    name: '[bq34]'
    type: 1
    effectType: 0
    effectID: emoji_34
  - uniqueKey: 35
    index: 35
    groupID: 1
    id: 35
    name: '[bq35]'
    type: 1
    effectType: 0
    effectID: emoji_35
  - uniqueKey: 36
    index: 36
    groupID: 1
    id: 36
    name: '[bq36]'
    type: 1
    effectType: 0
    effectID: emoji_36
  - uniqueKey: 37
    index: 37
    groupID: 1
    id: 37
    name: '[bq37]'
    type: 1
    effectType: 0
    effectID: emoji_37
  - uniqueKey: 38
    index: 38
    groupID: 1
    id: 38
    name: '[bq38]'
    type: 1
    effectType: 0
    effectID: emoji_38
  - uniqueKey: 39
    index: 39
    groupID: 1
    id: 39
    name: '[bq39]'
    type: 1
    effectType: 0
    effectID: emoji_39
  - uniqueKey: 40
    index: 40
    groupID: 1
    id: 40
    name: '[bq40]'
    type: 1
    effectType: 0
    effectID: emoji_40
  - uniqueKey: 41
    index: 41
    groupID: 1000
    id: 1
    name: '[zz01]'
    type: 2
    effectType: 1
    effectID: sticker_1000_01
  - uniqueKey: 42
    index: 42
    groupID: 1000
    id: 2
    name: '[zz02]'
    type: 2
    effectType: 1
    effectID: sticker_1000_02
  - uniqueKey: 43
    index: 43
    groupID: 1000
    id: 3
    name: '[zz03]'
    type: 2
    effectType: 1
    effectID: sticker_1000_03
  - uniqueKey: 44
    index: 44
    groupID: 1000
    id: 4
    name: '[zz04]'
    type: 2
    effectType: 1
    effectID: sticker_1000_04
  - uniqueKey: 45
    index: 45
    groupID: 1000
    id: 5
    name: '[zz05]'
    type: 2
    effectType: 1
    effectID: sticker_1000_05
  - uniqueKey: 46
    index: 46
    groupID: 1000
    id: 6
    name: '[zz06]'
    type: 2
    effectType: 1
    effectID: sticker_1000_06
  - uniqueKey: 47
    index: 47
    groupID: 1000
    id: 7
    name: '[zz07]'
    type: 2
    effectType: 1
    effectID: sticker_1000_07
  - uniqueKey: 48
    index: 48
    groupID: 1000
    id: 8
    name: '[zz08]'
    type: 2
    effectType: 1
    effectID: sticker_1000_08
  - uniqueKey: 49
    index: 49
    groupID: 1000
    id: 9
    name: '[zz09]'
    type: 2
    effectType: 1
    effectID: sticker_1000_09
  - uniqueKey: 50
    index: 50
    groupID: 1000
    id: 10
    name: '[zz10]'
    type: 2
    effectType: 1
    effectID: sticker_1000_10
