using System;
using System.Collections.Generic;
using LitJson;
using UnityEngine;

[Serializable]
public class TableSO : ScriptableObject
{
    //以下逻辑只在编辑器中有效
#if UNITY_EDITOR
    public static string[] tableNames = new string[] {
        "Equip",
        "GateTemplate",
        "Monster",
        "Weapon",
        "Bullet",
        "Level",
        "DropItem",
        "Map",
        "Buff",
        // "<PERSON>",
        "Item",
        "Langui",
        "LangError",
        "Skill",
        "Entry",
        "Guide",
        "UserAgreement",
        "Share",
        "ResourceGuide",
        "GateMonsterShow",
        "HelpTips",
        "BattleGuide",
        "BorderInfo",
        "CardFrame",
        "TreasureRaidersMonsterWava",
        "TreasureRaidersBuffItem",
        "MidWayMonsterWave",
        "MidWayBuffItem",
        "FlyBulletBuffItem",
        "FlyBulletMonsterWave",
        "dragonMonsters",
        "dragonBuffItems",
        "DragonGearMonster",
        "DragonGearHero",
        "DragonGearRate",
        "hero_battle_buff_effect_config",
        "hero_battle_skill_config",
        "ChatSticker",
        "ChatDeco",
        "RoleName",
    };

    public static ScriptableObject GenerateSO(string tableName, JsonData json)
    {
        switch (tableName)
        {
            case "Equip":
                return LoadFromJson<EquipSo, InfoEquip>(json);
            case "GateTemplate":
                return LoadFromJson<GateTemplateSo, InfoGateTemplate>(json);
            case "Monster":
                return LoadFromJson<MonsterSo, InfoMonster>(json);
            case "Weapon":
                return LoadFromJson<WeaponSo, InfoWeapon>(json);
            case "Bullet":
                return LoadFromJson<BulletSo, InfoBullet>(json);
            case "Level":
                return LoadFromJson<LevelSo, InfoLevel>(json);
            case "DropItem":
                return LoadFromJson<DropItemSo, InfoDropItem>(json);
            case "Map":
                return LoadFromJson<MapSo, InfoMap>(json);
            case "Buff":
                return LoadFromJson<BuffSo, InfoBuff>(json);
            case "Pet":
                return LoadFromJson<PetSo, InfoPet>(json);
            case "Item":
                return LoadFromJson<ItemSo, InfoItem>(json);
            case "Langui":
                return LoadFromJson<LanguiSo, InfoLang>(json);
            case "LangError":
                return LoadFromJson<LangErrorSo, InfoLang>(json);
            case "Skill":
                return LoadFromJson<SkillSo, InfoSkill>(json);
            case "Entry":
                return LoadFromJson<EntrySo, InfoEntry>(json);
            case "Guide":
                return LoadFromJson<GuideSo, InfoGuide>(json);
            case "UserAgreement":
                return LoadFromJson<UserAgreementSo, InfoUserAgreement>(json);
            case "Share":
                return LoadFromJson<ShareSo, InfoShare>(json);
            case "ResourceGuide":
                return LoadFromJson<ResourceGuideSo, InfoResourceGuide>(json);
            case "GateMonsterShow":
                return LoadFromJson<GateMonsterShowSo, InfoGateMonsterShow>(json);
            case "HelpTips":
                return LoadFromJson<HelpTipsSo, InfoHelpTip>(json);
            case "BattleGuide":
                var so = CreateInstance<BattleGuideSo>();
                so.datas = CreateArray<InfoBattleGuide>(json);
                return so as BattleGuideSo;

                return LoadFromJson<BattleGuideSo, InfoBattleGuide>(json);
            case "BorderInfo":
                return LoadFromJson<BorderInfoSo, InfoBorderInfo>(json);
            case "CardFrame":
                return LoadFromJson<CardFrameSo, InfoCardFrame>(json);
            case "FlyBulletMonsterWave":
                return LoadFromJson<FlyBulletMonsterWaveSo, InfoFlyBulletMonster>(json);
            case "FlyBulletBuffItem":
                return LoadFromJson<FlyBulletBuffItemSo, InfoFlyBulletBuffItem>(json);
            case "MidWayBuffItem":
                return LoadFromJson<MidWayBuffItemSo, InfoMidWayBuffItem>(json);
            case "MidWayMonsterWave":
                return LoadFromJson<MidWayMonsterWaveSo, InfoMidWayMonster>(json);
            case "TreasureRaidersBuffItem":
                return LoadFromJson<TreasureRaidersBuffItemSo, InfoTreasureRaidersBuffItem>(json);
            case "TreasureRaidersMonsterWava":
                return LoadFromJson<TreasureRaidersMonsterWavaSo, InfoTreasureRaidersMonster>(json);
            case "dragonMonsters":
                return LoadFromJson<DragonMonstersSo, InfoDragonMonster>(json);
            case "dragonBuffItems":
                return LoadFromJson<DragonBuffItemsSo, InfoDragonBuffItem>(json);
            case "DragonGearMonster":
                return LoadFromJson<DragonGearMonsterSo, InfoDragonGearMonster>(json);
            case "DragonGearHero":
                return LoadFromJson<DragonGearHeroSo, InfoDragonGearHero>(json);
            case "DragonGearRate":
                return LoadFromJson<DragonGearRateSo, InfoDragonGearRate>(json);
            case "hero_battle_skill_config":
                return LoadFromJson<HeroBattleSkillConfigSo, InfoPVPSkill>(json);
            case "hero_battle_buff_effect_config":
                return LoadFromJson<HeroBattleBuffEffectConfigSo, InfoPVPBuff>(json);
            case "ChatSticker":
                return LoadFromJson<ChatStickerSo, InfoSticker>(json);
            case "ChatDeco":
                return LoadFromJson<ChatDecoSo, InfoChatDeco>(json);
            case "RoleName":
                return LoadFromJson<RoleNameSo, InfoRoleName>(json);
        }

        return null;
    }

    public class BattleGuideSo : BaseSO
    {
        public InfoBattleGuide[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoBattleGuide[]; }
    }
    public class EquipSo : BaseSO
    {
        public InfoEquip[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoEquip[]; }
    }
    public class GateTemplateSo : BaseSO
    {
        public InfoGateTemplate[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoGateTemplate[]; }
    }
    public class MonsterSo : BaseSO
    {
        public InfoMonster[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoMonster[]; }
    }
    public class WeaponSo : BaseSO
    {
        public InfoWeapon[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoWeapon[]; }
    }
    public class BulletSo : BaseSO
    {
        public InfoBullet[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoBullet[]; }
    }
    public class LevelSo : BaseSO
    {
        public InfoLevel[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoLevel[]; }
    }
    public class DropItemSo : BaseSO
    {
        public InfoDropItem[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoDropItem[]; }
    }
    public class MapSo : BaseSO
    {
        public InfoMap[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoMap[]; }
    }
    public class BuffSo : BaseSO
    {
        public InfoBuff[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoBuff[]; }
    }
    public class PetSo : BaseSO
    {
        public InfoPet[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoPet[]; }
    }
    public class ItemSo : BaseSO
    {
        public InfoItem[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoItem[]; }
    }
    public class LanguiSo : BaseSO
    {
        public InfoLang[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoLang[]; }
    }
    public class LangErrorSo : BaseSO
    {
        public InfoLang[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoLang[]; }
    }
    public class SkillSo : BaseSO
    {
        public InfoSkill[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoSkill[]; }
    }
    public class EntrySo : BaseSO
    {
        public InfoEntry[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoEntry[]; }
    }
    public class GuideSo : BaseSO
    {
        public InfoGuide[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoGuide[]; }
    }
    public class UserAgreementSo : BaseSO
    {
        public InfoUserAgreement[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoUserAgreement[]; }
    }
    public class ShareSo : BaseSO
    {
        public InfoShare[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoShare[]; }
    }
    public class ResourceGuideSo : BaseSO
    {
        public InfoResourceGuide[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoResourceGuide[]; }
    }
    public class GateMonsterShowSo : BaseSO
    {
        public InfoGateMonsterShow[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoGateMonsterShow[]; }
    }
    public class HelpTipsSo : BaseSO
    {
        public InfoHelpTip[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoHelpTip[]; }
    }
    public class BorderInfoSo : BaseSO
    {
        public InfoBorderInfo[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoBorderInfo[]; }
    }
    public class CardFrameSo : BaseSO
    {
        public InfoCardFrame[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoCardFrame[]; }
    }
    public class FlyBulletMonsterWaveSo : BaseSO
    {
        public InfoFlyBulletMonster[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoFlyBulletMonster[]; }
    }
    public class FlyBulletBuffItemSo : BaseSO
    {
        public InfoFlyBulletBuffItem[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoFlyBulletBuffItem[]; }
    }
    public class MidWayBuffItemSo : BaseSO
    {
        public InfoMidWayBuffItem[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoMidWayBuffItem[]; }
    }
    public class MidWayMonsterWaveSo : BaseSO
    {
        public InfoMidWayMonster[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoMidWayMonster[]; }
    }
    public class TreasureRaidersBuffItemSo : BaseSO
    {
        public InfoTreasureRaidersBuffItem[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoTreasureRaidersBuffItem[]; }
    }
    public class TreasureRaidersMonsterWavaSo : BaseSO
    {
        public InfoTreasureRaidersMonster[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoTreasureRaidersMonster[]; }
    }
    public class DragonMonstersSo : BaseSO
    {
        public InfoDragonMonster[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoDragonMonster[]; }
    }
    public class DragonBuffItemsSo : BaseSO
    {
        public InfoDragonBuffItem[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoDragonBuffItem[]; }
    }
    public class DragonGearMonsterSo : BaseSO
    {
        public InfoDragonGearMonster[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoDragonGearMonster[]; }
    }
    public class DragonGearHeroSo : BaseSO
    {
        public InfoDragonGearHero[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoDragonGearHero[]; }
    }
    public class DragonGearRateSo : BaseSO
    {
        public InfoDragonGearRate[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoDragonGearRate[]; }
    }
    public class HeroBattleSkillConfigSo : BaseSO
    {
        public InfoPVPSkill[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoPVPSkill[]; }
    }
    public class HeroBattleBuffEffectConfigSo : BaseSO
    {
        public InfoPVPBuff[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoPVPBuff[]; }
    }
    public class ChatStickerSo : BaseSO
    {
        public InfoSticker[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoSticker[]; }
    }
    public class ChatDecoSo : BaseSO
    {
        public InfoChatDeco[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoChatDeco[]; }
    }
    public class RoleNameSo : BaseSO
    {
        public InfoRoleName[] _datas;
        public override ConfigData[] datas { get => _datas; set => _datas = value as InfoRoleName[]; }
    }

    public static BaseSO LoadFromJson<So, Info>(JsonData json) where So : BaseSO, new() where Info : ConfigData, new()
    {
        var so = CreateInstance<So>();
        so.datas = TableSO.CreateArray<Info>(json);
        return so as So;
    }

    public static T[] CreateArray<T>(JsonData json) where T : ConfigData, new()
    {
        var list = new List<T>();
        if (json.IsArray)
        {
            for (int i = 0; i < json.Count; i++)
            {
                T data = new T();
                data.Init(i, json[i]);
                data.uniqueKey = data.key.ToString();
                list.Add(data);
            }
        }
        else
        {
            string[] keys = new string[json.Count];
            json.Keys.CopyTo(keys, 0);
            for (int i = 0; i < keys.Length; i++)
            {
                string key = keys[i];
                T data = new T();
                data.Init(key, json[key]);
                data.uniqueKey = data.key.ToString();
                list.Add(data);
            }
        }
        var result = list.ToArray();
        return result;
    }
#endif
}