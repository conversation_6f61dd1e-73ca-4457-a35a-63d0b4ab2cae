using System;
using System.Collections.Generic;
using dnlib.DotNet.MD;
using LitJson;
using UnityEngine;

[Serializable]
public class TableSO : ScriptableObject
{
    public static TableInfo[] tables = new TableInfo[]{
        new("Equip",typeof(ConfigEquip),typeof(InfoEquip),typeof(EquipSo))
    };

    public class TableInfo
    {
        public string name;
        public Type configType;
        public Type infoType;
        public Type soType;
        public TableInfo(string name, Type configType, Type infoType, Type soType)
        {
            this.name = name;
            this.configType = configType;
            this.infoType = infoType;
            this.soType = soType;
        }
    }

    //以下逻辑只在编辑器中有效
#if UNITY_EDITOR
    public static string[] tableNames = new string[] {
    "Equip",
    "GateTemplate",
    "Monster",
    "Weapon",
    "Bullet",
    "Level",
    "DropItem",
    "Map",
    "Buff",
    // "<PERSON>",
    "Item",
    "<PERSON><PERSON>",
    "LangError",
    "Skill",
    "Entry",
    "Guide",
    "UserAgreement",
    "Share",
    "ResourceGuide",
    "GateMonsterShow",
    "HelpTips",
    "BattleGuide",
    "BorderInfo",
    "CardFrame",
    "TreasureRaidersMonsterWava",
    "TreasureRaidersBuffItem",
    "MidWayMonsterWave",
    "MidWayBuffItem",
    "FlyBulletBuffItem",
    "FlyBulletMonsterWave",
    "DragonMonster",
    "DragonBuffItems",
    "DragonGearMonster",
    "DragonGearHero",
    "DragonGearRate",
    "hero_battle_buff_effect_config",
    "hero_battle_skill_config",
    "ChatSticker",
    "ChatDeco",
    "RoleName",
};

    public static ScriptableObject GenerateSO(string tableName, JsonData json)
    {
        switch (tableName)
        {
            case "Equip":
                return LoadFromJson<EquipSo, InfoEquip>(json);
            case "GateTemplate":
                return LoadFromJson<GateTemplateSo, InfoGateTemplate>(json);
            case "Monster":
                return LoadFromJson<MonsterSo, InfoMonster>(json);
            case "Weapon":
                return LoadFromJson<WeaponSo, InfoWeapon>(json);
            case "Bullet":
                return LoadFromJson<BulletSo, InfoBullet>(json);
            case "Level":
                return LoadFromJson<LevelSo, InfoLevel>(json);
            case "DropItem":
                return LoadFromJson<DropItemSo, InfoDropItem>(json);
            case "Map":
                return LoadFromJson<MapSo, InfoMap>(json);
            case "Buff":
                return LoadFromJson<BuffSo, InfoBuff>(json);
            case "Pet":
                return LoadFromJson<PetSo, InfoPet>(json);
            case "Item":
                return LoadFromJson<ItemSo, InfoItem>(json);
            case "Langui":
                return LoadFromJson<LanguiSo, InfoLang>(json);
            case "LangError":
                return LoadFromJson<LangErrorSo, InfoLang>(json);
            case "Skill":
                return LoadFromJson<SkillSo, InfoSkill>(json);
            case "Entry":
                return LoadFromJson<EntrySo, InfoEntry>(json);
            case "Guide":
                return LoadFromJson<GuideSo, InfoGuide>(json);
            case "UserAgreement":
                return LoadFromJson<UserAgreementSo, InfoUserAgreement>(json);
            case "Share":
                return LoadFromJson<ShareSo, InfoShare>(json);
            case "ResourceGuide":
                return LoadFromJson<ResourceGuideSo, InfoResourceGuide>(json);
            case "GateMonsterShow":
                return LoadFromJson<GateMonsterShowSo, InfoGateMonsterShow>(json);
            case "HelpTips":
                return LoadFromJson<HelpTipsSo, InfoHelpTip>(json);
            case "BattleGuide":
                return LoadFromJson<BattleGuideSo, InfoBattleGuide>(json);
            case "BorderInfo":
                return LoadFromJson<BorderInfoSo, InfoBorderInfo>(json);
            case "CardFrame":
                return LoadFromJson<CardFrameSo, InfoCardFrame>(json);
            case "FlyBulletMonsterWave":
                return LoadFromJson<FlyBulletMonsterWaveSo, InfoFlyBulletMonster>(json);
            case "FlyBulletBuffItem":
                return LoadFromJson<FlyBulletBuffItemSo, InfoFlyBulletBuffItem>(json);
            case "MidWayBuffItem":
                return LoadFromJson<MidWayBuffItemSo, InfoMidWayBuffItem>(json);
            case "MidWayMonsterWave":
                return LoadFromJson<MidWayMonsterWaveSo, InfoMidWayMonster>(json);
            case "TreasureRaidersBuffItem":
                return LoadFromJson<TreasureRaidersBuffItemSo, InfoTreasureRaidersBuffItem>(json);
            case "TreasureRaidersMonsterWava":
                return LoadFromJson<TreasureRaidersMonsterWavaSo, InfoTreasureRaidersMonster>(json);
            case "DragonMonster":
                return LoadFromJson<DragonMonstersSo, InfoDragonMonster>(json);
            case "DragonBuffItems":
                return LoadFromJson<DragonBuffItemsSo, InfoDragonBuffItem>(json);
            case "DragonGearMonster":
                return LoadFromJson<DragonGearMonsterSo, InfoDragonGearMonster>(json);
            case "DragonGearHero":
                return LoadFromJson<DragonGearHeroSo, InfoDragonGearHero>(json);
            case "DragonGearRate":
                return LoadFromJson<DragonGearRateSo, InfoDragonGearRate>(json);
            case "hero_battle_skill_config":
                return LoadFromJson<HeroBattleSkillConfigSo, InfoPVPSkill>(json);
            case "hero_battle_buff_effect_config":
                return LoadFromJson<HeroBattleBuffEffectConfigSo, InfoPVPBuff>(json);
            case "ChatSticker":
                return LoadFromJson<ChatStickerSo, InfoSticker>(json);
            case "ChatDeco":
                return LoadFromJson<ChatDecoSo, InfoChatDeco>(json);
            case "RoleName":
                return LoadFromJson<RoleNameSo, InfoRoleName>(json);
        }

        return null;
    }




    public static BaseSO LoadFromJson<So, Info>(JsonData json) where So : BaseSO, new() where Info : ConfigData, new()
    {
        var so = CreateInstance<So>();
        so.datas = TableSO.CreateArray<Info>(json);
        return so as So;
    }

    public static T[] CreateArray<T>(JsonData json) where T : ConfigData, new()
    {
        var list = new List<T>();
        if (json.IsArray)
        {
            for (int i = 0; i < json.Count; i++)
            {
                T data = new T();
                data.Init(i, json[i]);
                data.uniqueKey = data.key.ToString();
                list.Add(data);
            }
        }
        else
        {
            string[] keys = new string[json.Count];
            json.Keys.CopyTo(keys, 0);
            for (int i = 0; i < keys.Length; i++)
            {
                string key = keys[i];
                T data = new T();
                data.Init(key, json[key]);
                data.uniqueKey = data.key.ToString();
                list.Add(data);
            }
        }
        var result = list.ToArray();
        return result;
    }
#endif
}