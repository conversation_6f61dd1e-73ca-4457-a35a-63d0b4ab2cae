%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: GuideSO
  m_EditorClassIdentifier: Assembly-CSharp::TableSO/GuideSo
  _datas:
  - uniqueKey: 1000
    id: 1000
    triggerType: 0
    triggerParam: 0
    name: PageChest
    target: btnChest
    type: 1
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 1001
    id: 1001
    triggerType: 0
    triggerParam: 0
    name: PageChest
    target: btnChestLevel
    type: 1
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 1010
    id: 1010
    triggerType: 0
    triggerParam: 0
    name: PageChest
    target: btnChestLevel
    type: 1
    showEff: 1
    nextId: 1011
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 1011
    id: 1011
    triggerType: 0
    triggerParam: 0
    name: ChestTalentPanel
    target: btnUpgradeStar
    type: 1
    showEff: 1
    nextId: 1012
    param: 0
    offset: 140_50
    delay: 0
    param1: 
  - uniqueKey: 1012
    id: 1012
    triggerType: 0
    triggerParam: 0
    name: ChestTalentPanel
    target: btnUpgradeStar
    type: 1
    showEff: 1
    nextId: 1013
    param: 0
    offset: 140_50
    delay: 0
    param1: 
  - uniqueKey: 1013
    id: 1013
    triggerType: 0
    triggerParam: 0
    name: ChestTalentPanel
    target: btnUpgradeStar
    type: 1
    showEff: 1
    nextId: 1014
    param: 0
    offset: 140_50
    delay: 0
    param1: 
  - uniqueKey: 1014
    id: 1014
    triggerType: 0
    triggerParam: 0
    name: ChestTalentPanel
    target: btnUpgrade
    type: 1
    showEff: 1
    nextId: 1015
    param: 0
    offset: 140_50
    delay: 0
    param1: 
  - uniqueKey: 1015
    id: 1015
    triggerType: 0
    triggerParam: 0
    name: ChestTalentPanel
    target: btnSpeed
    type: 1
    showEff: 1
    nextId: 1016
    param: 0
    offset: 140_50
    delay: 0
    param1: 
  - uniqueKey: 1016
    id: 1016
    triggerType: 0
    triggerParam: 0
    name: UserPropsPanel
    target: btnUse
    type: 1
    showEff: 1
    nextId: 0
    param: 0
    offset: 140_50
    delay: 0
    param1: 
  - uniqueKey: 2000
    id: 2000
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_2
    type: 3
    showEff: 1
    nextId: 2001
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 2001
    id: 2001
    triggerType: 0
    triggerParam: 0
    name: MapListPanel
    target: 
    type: 2
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 2100
    id: 2100
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_2
    type: 3
    showEff: 1
    nextId: 2101
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 2101
    id: 2101
    triggerType: 0
    triggerParam: 0
    name: MapListPanel
    target: 1002
    type: 2
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 2200
    id: 2200
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_2
    type: 3
    showEff: 1
    nextId: 2201
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 2201
    id: 2201
    triggerType: 0
    triggerParam: 0
    name: MapListPanel
    target: 1004
    type: 2
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 2300
    id: 2300
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_2
    type: 3
    showEff: 1
    nextId: 2301
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 2301
    id: 2301
    triggerType: 0
    triggerParam: 0
    name: MapListPanel
    target: 1006
    type: 2
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 2400
    id: 2400
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_2
    type: 3
    showEff: 1
    nextId: 2401
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 2401
    id: 2401
    triggerType: 0
    triggerParam: 0
    name: MapListPanel
    target: 1008
    type: 2
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 3000
    id: 3000
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 3100
    id: 3100
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 3101
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 3101
    id: 3101
    triggerType: 0
    triggerParam: 0
    name: HeroListPanel
    target: BtnArraying
    type: 1
    showEff: 1
    nextId: 3102
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 3102
    id: 3102
    triggerType: 0
    triggerParam: 0
    name: HeroArrayingPanel
    target: listHero_3
    type: 3
    showEff: 1
    nextId: 3103
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 3103
    id: 3103
    triggerType: 0
    triggerParam: 0
    name: HeroArrayingPanel
    target: btnClose
    type: 1
    showEff: 1
    nextId: 3104
    param: 0
    offset: 51_43
    delay: 0
    param1: 
  - uniqueKey: 3104
    id: 3104
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 4000
    id: 4000
    triggerType: 0
    triggerParam: 0
    name: PageChest
    target: btnGacha
    type: 1
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 4010
    id: 4010
    triggerType: 0
    triggerParam: 0
    name: PageChest
    target: btnGacha
    type: 1
    showEff: 1
    nextId: 4011
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 4011
    id: 4011
    triggerType: 0
    triggerParam: 0
    name: GachaPanel
    target: boxGachaBase|btnExtract1
    type: 5
    showEff: 1
    nextId: 0
    param: 0
    offset: 140_50
    delay: 0
    param1: 
  - uniqueKey: 5100
    id: 5100
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 5101
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 5101
    id: 5101
    triggerType: 0
    triggerParam: 0
    name: HeroListPanel
    target: BtnTalent
    type: 1
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 5110
    id: 5110
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 5111
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 5111
    id: 5111
    triggerType: 0
    triggerParam: 0
    name: HeroListPanel
    target: BtnTalent
    type: 1
    showEff: 1
    nextId: 5112
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 5112
    id: 5112
    triggerType: 0
    triggerParam: 0
    name: NewTalentPanel
    target: 
    type: 2
    showEff: 1
    nextId: 5113
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 5113
    id: 5113
    triggerType: 0
    triggerParam: 0
    name: baseTip
    target: btnUnlock
    type: 1
    showEff: 1
    nextId: 5114
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 5114
    id: 5114
    triggerType: 0
    triggerParam: 0
    name: NewTalentPanel
    target: 
    type: 2
    showEff: 1
    nextId: 5115
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 5115
    id: 5115
    triggerType: 0
    triggerParam: 0
    name: baseTip
    target: btnUnlock
    type: 1
    showEff: 1
    nextId: 5116
    param: 0
    offset: ' '
    delay: 0
    param1: 
  - uniqueKey: 5116
    id: 5116
    triggerType: 0
    triggerParam: 0
    name: NewTalentPanel
    target: 
    type: 2
    showEff: 1
    nextId: 5117
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 5117
    id: 5117
    triggerType: 0
    triggerParam: 0
    name: baseTip
    target: btnUnlock
    type: 1
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 5200
    id: 5200
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 5201
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 5201
    id: 5201
    triggerType: 0
    triggerParam: 0
    name: HeroListPanel
    target: BtnTalent
    type: 1
    showEff: 1
    nextId: 5202
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 5202
    id: 5202
    triggerType: 0
    triggerParam: 0
    name: NewTalentPanel
    target: btnAllUnlock
    type: 1
    showEff: 1
    nextId: 0
    param: 0
    offset: 140_50
    delay: 0
    param1: 
  - uniqueKey: 6000
    id: 6000
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_3
    type: 3
    showEff: 1
    nextId: 6001
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 6001
    id: 6001
    triggerType: 0
    triggerParam: 0
    name: BasePanel
    target: btnForge
    type: 1
    showEff: 1
    nextId: 6002
    param: 0
    offset: 125_170
    delay: 0
    param1: 
  - uniqueKey: 6002
    id: 6002
    triggerType: 0
    triggerParam: 0
    name: ForgeDecoration
    target: btnOneEnhance
    type: 1
    showEff: 1
    nextId: 0
    param: 0
    offset: 60_56
    delay: 0
    param1: 
  - uniqueKey: 7000
    id: 7000
    triggerType: 0
    triggerParam: 0
    name: PageChest
    target: btnPatrol
    type: 1
    showEff: 1
    nextId: 7001
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 7001
    id: 7001
    triggerType: 0
    triggerParam: 0
    name: PatrolPanel
    target: btnReceive
    type: 1
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 11000
    id: 11000
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 11001
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 11001
    id: 11001
    triggerType: 0
    triggerParam: 0
    name: HeroListPanel
    target: BtnArraying
    type: 1
    showEff: 1
    nextId: 11002
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 11002
    id: 11002
    triggerType: 0
    triggerParam: 0
    name: HeroArrayingPanel
    target: listHero_4
    type: 3
    showEff: 1
    nextId: 11003
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 11003
    id: 11003
    triggerType: 0
    triggerParam: 0
    name: HeroArrayingPanel
    target: btnClose
    type: 1
    showEff: 1
    nextId: 11004
    param: 0
    offset: 51_43
    delay: 0
    param1: 
  - uniqueKey: 11004
    id: 11004
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 12000
    id: 12000
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 12001
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 12001
    id: 12001
    triggerType: 0
    triggerParam: 0
    name: HeroListPanel
    target: BtnArraying
    type: 1
    showEff: 1
    nextId: 12002
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 12002
    id: 12002
    triggerType: 0
    triggerParam: 0
    name: HeroArrayingPanel
    target: listHero_5
    type: 3
    showEff: 1
    nextId: 12003
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 12003
    id: 12003
    triggerType: 0
    triggerParam: 0
    name: HeroArrayingPanel
    target: btnClose
    type: 1
    showEff: 1
    nextId: 12004
    param: 0
    offset: 51_43
    delay: 0
    param1: 
  - uniqueKey: 12004
    id: 12004
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 13000
    id: 13000
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_4
    type: 3
    showEff: 1
    nextId: 13001
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 13001
    id: 13001
    triggerType: 0
    triggerParam: 0
    name: ChallengeGatePanel
    target: listGate
    type: 4
    showEff: 1
    nextId: 0
    param: 34040
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 13200
    id: 13200
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_4
    type: 3
    showEff: 1
    nextId: 13201
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 13201
    id: 13201
    triggerType: 0
    triggerParam: 0
    name: ChallengeGatePanel
    target: listGate
    type: 4
    showEff: 1
    nextId: 0
    param: 34060
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 13300
    id: 13300
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_2
    type: 3
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 13600
    id: 13600
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 13601
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 13601
    id: 13601
    triggerType: 0
    triggerParam: 0
    name: HeroListPanel
    target: BtnArraying
    type: 1
    showEff: 1
    nextId: 13602
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 13602
    id: 13602
    triggerType: 0
    triggerParam: 0
    name: HeroArrayingPanel
    target: listHero_4
    type: 3
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 13700
    id: 13700
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 13701
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 13701
    id: 13701
    triggerType: 0
    triggerParam: 0
    name: HeroListPanel
    target: btnAllLevelUp
    type: 1
    showEff: 1
    nextId: 13702
    param: 0
    offset: 140_50
    delay: 0
    param1: 
  - uniqueKey: 13702
    id: 13702
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_1
    type: 3
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20000
    id: 20000
    triggerType: 1
    triggerParam: 32
    name: PageChest
    target: 
    type: 6
    showEff: 0
    nextId: 20001
    param: "[color=#f96f00][size=39]\u7926\u6D1E\u8FF7\u5BAE[/size][/color]\u958B\u653E\u5566~\u8EBA\u5E73\u6316\u7926\uFF0C\u8F15\u9B06\u4F11\u9592\u62FF[color=#f96f00][size=39]\u5BF6\u77F3[/size][/color]\uFF0C\u5FEB\u4F86\u770B\u770B\u5427~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20001
    id: 20001
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_4
    type: 3
    showEff: 1
    nextId: 20002
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20002
    id: 20002
    triggerType: 0
    triggerParam: 0
    name: ChallengeGatePanel
    target: listGate
    type: 4
    showEff: 1
    nextId: 20003
    param: 34050
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20003
    id: 20003
    triggerType: 0
    triggerParam: 0
    name: GemMinePanel
    target: 
    type: 6
    showEff: 0
    nextId: 20004
    param: "\u9019\u88E1\u5C31\u662F\u7926\u6D1E\u5566~\u6316\u7926\u9700\u8981\u7684[color=#f96f00][size=39]\u93AC\u5B50[/size][/color]\u53EF\u4EE5\u901A\u904E[color=#f96f00][size=39]\u958B\u5FEB\u905E[/size][/color]\u7372\u5F97\u54E6~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20004
    id: 20004
    triggerType: 0
    triggerParam: 0
    name: GemMinePanel
    target: 
    type: 6
    showEff: 0
    nextId: 20005
    param: "\u6B64\u5916\uFF0C\u6316\u7926\u7372\u5F97\u7684[color=#f96f00][size=39]\u539F\u77F3[/size][/color]\u53EF\u4EE5\u63D0\u7149\u6210\u53EF\u9472\u5D4C\u7684[color=#f96f00][size=39]\u5BF6\u77F3[/size][/color]~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20005
    id: 20005
    triggerType: 0
    triggerParam: 0
    name: GemMinePanel
    target: 
    type: 6
    showEff: 0
    nextId: 0
    param: "\u9084\u6709\u4E0D\u6E05\u695A\u7684\u4E5F\u53EF\u4EE5\u67E5\u770B[color=#f96f00][size=39]\u898F\u5247\u8AAA\u660E[/size][/color]\u8A73\u7D30\u77AD\u89E3\u4E0B\u54E6~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20100
    id: 20100
    triggerType: 1
    triggerParam: 31
    name: PageChest
    target: 
    type: 6
    showEff: 0
    nextId: 20101
    param: "[color=#f96f00][size=39]\u751F\u5316\u5371\u96DE[/size][/color]\u958B\u555F\u5566~\u7522\u51FA[color=#f96f00][size=39]\u50B3\u5947\u82F1\u96C4\u788E\u7247\u3001\u5347\u661F\u77F3[/size][/color]\u7B49\u7A00\u6709\u734E\u52F5\uFF0C\u5FEB\u4F86\u770B\u770B\u5427~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20101
    id: 20101
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_4
    type: 3
    showEff: 1
    nextId: 20102
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20102
    id: 20102
    triggerType: 0
    triggerParam: 0
    name: ChallengeGatePanel
    target: listGate
    type: 4
    showEff: 1
    nextId: 0
    param: 34040
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20200
    id: 20200
    triggerType: 1
    triggerParam: 43
    name: PageChest
    target: 
    type: 6
    showEff: 0
    nextId: 20201
    param: "[color=#f96f00][size=39]\u5BF5\u7269[/size][/color]\u4F86\u5566~\u80FD\u5920\u5E36\u4F86\u5927\u91CF[color=#f96f00][size=39]\u82F1\u96C4\u5C6C\u6027\u653B\u64CA[/size][/color]\uFF0C\u89E3\u9396[color=#f96f00][size=39]\u65B0\u6280\u80FD[/size][/color]\uFF0C\u5FEB\u4F86\u770B\u770B\u5427~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20201
    id: 20201
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_0
    type: 3
    showEff: 1
    nextId: 20202
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20202
    id: 20202
    triggerType: 0
    triggerParam: 0
    name: GGIslandPanel
    target: btnPet
    type: 1
    showEff: 1
    nextId: 20203
    param: 0
    offset: 100_100
    delay: 0
    param1: 
  - uniqueKey: 20203
    id: 20203
    triggerType: 0
    triggerParam: 0
    name: PetPanel
    target: 
    type: 6
    showEff: 0
    nextId: 0
    param: "\u5BF5\u7269\u5C6C\u6027\u555F\u52D5\u5373\u53EF\u7372\u5F97\uFF0C\u4E26\u4E14\u53EF\u4EE5[color=#f96f00][size=39]\u758A\u52A0[/size][/color]\u54E6\uFF0C\u555F\u52D5\u6750\u6599\u53EF\u4EE5\u5F9E[color=#f96f00][size=39]\u79D8\u5883\u5C0B\u5BF6[/size][/color]\u7372\u5F97~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20300
    id: 20300
    triggerType: 1
    triggerParam: 44
    name: PageChest
    target: 
    type: 6
    showEff: 0
    nextId: 20301
    param: "[color=#f96f00][size=39]\u5BF6\u5177[/size][/color]\u4F86\u5566~\u80FD\u5920\u5E36\u4F86\u5927\u91CF[color=#f96f00][size=39]\u82F1\u96C4\u8077\u696D\u653B\u64CA[/size][/color]\uFF0C\u89E3\u9396[color=#f96f00][size=39]\u65B0\u6280\u80FD[/size][/color]\uFF0C\u5FEB\u4F86\u770B\u770B\u5427~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20301
    id: 20301
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_0
    type: 3
    showEff: 1
    nextId: 20302
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20302
    id: 20302
    triggerType: 0
    triggerParam: 0
    name: GGIslandPanel
    target: btnArtifact
    type: 1
    showEff: 1
    nextId: 20303
    param: 0
    offset: 100_100
    delay: 0
    param1: 
  - uniqueKey: 20303
    id: 20303
    triggerType: 0
    triggerParam: 0
    name: ArtifactPanel
    target: 
    type: 6
    showEff: 0
    nextId: 0
    param: "\u5BF6\u5177\u5C6C\u6027\u555F\u52D5\u5373\u53EF\u7372\u5F97\uFF0C\u4E26\u4E14\u53EF\u4EE5[color=#f96f00][size=39]\u758A\u52A0[/size][/color]\u54E6\uFF0C\u555F\u52D5\u6750\u6599\u53EF\u4EE5\u5F9E[color=#f96f00][size=39]\u6DF1\u6D77\u63A2\u5BF6[/size][/color]\u7372\u5F97~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20400
    id: 20400
    triggerType: 1
    triggerParam: 45
    name: PageChest
    target: 
    type: 6
    showEff: 0
    nextId: 20401
    param: "[color=#f96f00][size=39]\u57CE\u7246[/size][/color]\u4F86\u5566~\u80FD\u5920\u5E36\u4F86\u5927\u91CF[color=#f96f00][size=39]\u5C0F\u968A\u751F\u547D[/size][/color]\uFF0C\u89E3\u9396[color=#f96f00][size=39]\u65B0\u6280\u80FD[/size][/color]\uFF0C\u5FEB\u4F86\u770B\u770B\u5427~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20401
    id: 20401
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_0
    type: 3
    showEff: 1
    nextId: 20402
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20402
    id: 20402
    triggerType: 0
    triggerParam: 0
    name: GGIslandPanel
    target: btnWall
    type: 1
    showEff: 1
    nextId: 20403
    param: 0
    offset: 100_100
    delay: 0
    param1: 
  - uniqueKey: 20403
    id: 20403
    triggerType: 0
    triggerParam: 0
    name: BattleWallPanel
    target: 
    type: 6
    showEff: 0
    nextId: 0
    param: "\u57CE\u7246\u5C6C\u6027\u555F\u52D5\u5373\u53EF\u7372\u5F97\uFF0C\u4E26\u4E14\u53EF\u4EE5[color=#f96f00][size=39]\u758A\u52A0[/size][/color]\u54E6\uFF0C\u555F\u52D5\u6750\u6599\u53EF\u4EE5\u5F9E[color=#f96f00][size=39]\u795E\u9F8D\u5BF6\u85CF[/size][/color]\u7372\u5F97~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20500
    id: 20500
    triggerType: 1
    triggerParam: 48
    name: PageChest
    target: 
    type: 6
    showEff: 0
    nextId: 20501
    param: "[color=#f96f00][size=39]\u5DD4\u5CF0\u4E4B\u8DEF[/size][/color]\u6D3B\u52D5\u958B\u555F\u5566~[color=#f96f00][size=39]S\u50B3\u5947\u82F1\u96C4[/size][/color]\u975C\u5F85\u540D\u4E3B\uFF0C\u5FEB\u4F86\u770B\u770B\u5427~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20501
    id: 20501
    triggerType: 0
    triggerParam: 0
    name: PageChest
    target: btnPeakBattle
    type: 1
    showEff: 1
    nextId: 20502
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 20502
    id: 20502
    triggerType: 0
    triggerParam: 0
    name: PeakBattle
    target: 
    type: 6
    showEff: 0
    nextId: 0
    param: "\u5DD4\u5CF0\u4E4B\u8DEF\u734E\u52F5\u8C50\u5BCC\uFF1A[color=#f96f00][size=39]\u8CFD\u5B63\u734E\u52F5[/size][/color]\u3001[color=#f96f00][size=39]\u968E\u6BB5\u734E\u52F5[/size][/color]\u3001[color=#f96f00][size=39]\u53C3\u8207\u734E\u52F5[/size][/color]\u7B49\u7B49\uFF0C\u5FEB\u958B\u59CB\u6230\u9B25\u5427~"
    offset: 
    delay: 0.5
    param1: 
  - uniqueKey: 30000
    id: 30000
    triggerType: 0
    triggerParam: 0
    name: BattlePanel
    target: 
    type: 6
    showEff: 0
    nextId: 0
    param: "\u6575\u4EBA\u904E\u65BC\u5F37\u5927\uFF0C\u5446\u5B50\uFF0C\u6211\u5011\u4F86\u652F\u63F4\u4F60\u5566\uFF01"
    offset: 
    delay: 0
    param1: "image_head1|\u5B6B\u609F\u7A7A"
  - uniqueKey: 30001
    id: 30001
    triggerType: 0
    triggerParam: 0
    name: BattlePanel
    target: 
    type: 6
    showEff: 0
    nextId: 0
    param: "\u672C\u95DC\u6575\u65B9\u9996\u9818\u975E\u5E38\u5F37\u5927\uFF0C\u8ACB\u5118\u91CF\u62D6\u5EF6\u6642\u9593\uFF01"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30100
    id: 30100
    triggerType: 1
    triggerParam: 27
    name: PageChest
    target: 
    type: 6
    showEff: 0
    nextId: 30101
    param: "[color=#f96f00][size=39]\u5E78\u904B\u62DB\u52DF[/size][/color]\u76DB\u5927\u958B\u555F~\u62DB\u52DF[color=#f96f00][size=39]\u6307\u5B9A\u82F1\u96C4[/size][/color]\u66F4\u4FBF\u6377\uFF0C\u5FEB\u4F86\u770B\u770B\u5427~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30101
    id: 30101
    triggerType: 0
    triggerParam: 0
    name: PageChest
    target: btnGacha
    type: 1
    showEff: 1
    nextId: 0
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30200
    id: 30200
    triggerType: 1
    triggerParam: 46
    name: PageChest
    target: 
    type: 6
    showEff: 0
    nextId: 30201
    param: "[color=#f96f00][size=39]\u516C\u6703[/size][/color]\u958B\u555F\u5566~\u9019\u88E1\u53EF\u4EE5\u7D50\u8B58[color=#f96f00][size=39]\u65B0\u670B\u53CB[/size][/color]\uFF0C\u9084\u80FD\u53C3\u8207[color=#f96f00][size=39]\u516C\u6703\u8A0E\u4F10[/size][/color]\u7372\u5F97[color=#f96f00][size=39]\u7A00\u6709\u734E\u52F5[/size][/color]\uFF0C\u5FEB\u4F86\u770B\u770B\u5427~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30201
    id: 30201
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_0
    type: 3
    showEff: 1
    nextId: 30202
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30202
    id: 30202
    triggerType: 0
    triggerParam: 0
    name: GGIslandPanel
    target: btnGuild
    type: 1
    showEff: 1
    nextId: 30203
    param: 0
    offset: 100_100
    delay: 0
    param1: 
  - uniqueKey: 30203
    id: 30203
    triggerType: 0
    triggerParam: 0
    name: GuildPanel
    target: 
    type: 6
    showEff: 0
    nextId: 0
    param: "\u9084\u5728\u7B49\u4EC0\u9EBC\u5462\uFF1F\u5FEB\u5FEB\u52A0\u5165\u6216\u5275\u5EFA[color=#f96f00][size=39]\u516C\u6703[/size][/color]\u5927\u5BB6\u5EAD\u5427~\u53EF\u5225\u932F\u904E[color=#f96f00][size=39]\u7A00\u6709\u734E\u52F5[/size][/color]\u54E6~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30300
    id: 30300
    triggerType: 0
    triggerParam: 0
    name: ArenaPanel
    target: 
    type: 6
    showEff: 0
    nextId: 30301
    param: "\u7AF6\u6280\u5834\u5168\u5929\u5019\u958B\u653E\uFF0C\u6BCF\u5929\u53C3\u8207\u53EF\u9818\u53D6\u8C50\u539A\u734E\u52F5\uFF0C\u5FEB\u4F86[color=#f96f00][size=39]\u7DE8\u968A[/size][/color]\u5373\u523B\u6230\u9B25\u5427\uFF01"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30301
    id: 30301
    triggerType: 0
    triggerParam: 0
    name: ArenaPanel
    target: btn_arenaTeam
    type: 1
    showEff: 1
    nextId: 30302
    param: 0
    offset: 50_50
    delay: 0
    param1: 
  - uniqueKey: 30302
    id: 30302
    triggerType: 0
    triggerParam: 0
    name: ArenaReady
    target: 
    type: 6
    showEff: 0
    nextId: 30303
    param: "\u9700\u8981\u5C07\u9032\u653B\u8207\u9632\u5B88\u968A\u4F0D[color=#f96f00][size=39]\u7DE8\u597D\u968A\u4F0D[/size][/color]\u624D\u80FD\u9032\u884C\u6230\u9B25\uFF0C\u5FEB\u4F7F\u7528[color=#f96f00][size=39]\u4E00\u9375\u4E0A\u9663[/size][/color]\u5FEB\u901F\u7DE8\u968A\u5427~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30303
    id: 30303
    triggerType: 0
    triggerParam: 0
    name: ArenaReady
    target: btnStart
    type: 1
    showEff: 1
    nextId: 30304
    param: 0
    offset: 100_50
    delay: 0.5
    param1: 
  - uniqueKey: 30304
    id: 30304
    triggerType: 0
    triggerParam: 0
    name: ArenaReady
    target: listType_1
    type: 3
    showEff: 1
    nextId: 30305
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30305
    id: 30305
    triggerType: 0
    triggerParam: 0
    name: ArenaReady
    target: btnStart
    type: 1
    showEff: 1
    nextId: 0
    param: 0
    offset: 100_50
    delay: 0
    param1: 
  - uniqueKey: 30400
    id: 30400
    triggerType: 1
    triggerParam: 47
    name: PageChest
    target: 
    type: 6
    showEff: 0
    nextId: 30401
    param: "[color=#f96f00][size=39]\u7AF6\u6280\u5834[/size][/color]\u958B\u555F\u5566~\u5C55\u73FE\u5BE6\u529B\u7D55\u4F73\u821E\u81FA\uFF0C\u6D77\u91CF[color=#f96f00][size=39]\u7A00\u6709\u734E\u52F5[/size][/color]\u514D\u8CBB\u63DB\uFF0C\u5FEB\u4F86\u770B\u770B\u5427~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30401
    id: 30401
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_0
    type: 3
    showEff: 1
    nextId: 30402
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30402
    id: 30402
    triggerType: 0
    triggerParam: 0
    name: GGIslandPanel
    target: btnArena
    type: 1
    showEff: 1
    nextId: 0
    param: 0
    offset: 100_100
    delay: 0
    param1: 
  - uniqueKey: 30500
    id: 30500
    triggerType: 1
    triggerParam: 112
    name: PageChest
    target: 
    type: 6
    showEff: 0
    nextId: 30501
    param: "[color=#f96f00][size=39]\u6D77\u5CF6\u722D\u9738[/size][/color]\u958B\u555F\u5566~\u5360\u9818\u6D77\u5CF6\u7576\u5CF6\u4E3B\uFF0C\u66F4\u6709\u6D77\u91CF[color=#f96f00][size=39]\u73CD\u8CB4\u76AE\u819A[/size][/color]\u514D\u8CBB\u62FF\uFF0C\u5FEB\u4F86\u770B\u770B\u5427~"
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30501
    id: 30501
    triggerType: 0
    triggerParam: 0
    name: LobbyPanel
    target: listTab_0
    type: 3
    showEff: 1
    nextId: 30502
    param: 0
    offset: 
    delay: 0
    param1: 
  - uniqueKey: 30502
    id: 30502
    triggerType: 0
    triggerParam: 0
    name: GGIslandPanel
    target: btnIslandCraft
    type: 1
    showEff: 1
    nextId: 30503
    param: 0
    offset: 100_100
    delay: 0
    param1: 
  - uniqueKey: 30503
    id: 30503
    triggerType: 0
    triggerParam: 0
    name: IslandCraftPanel
    target: 
    type: 6
    showEff: 0
    nextId: 0
    param: "[color=#f96f00][size=39]\u5360\u9818\u6D77\u5CF6[/size][/color]\u7372\u5F97\u529F\u52DB\u5E63\uFF0C\u5C31\u80FD\u514C\u63DB[color=#f96f00][size=39]\u73CD\u8CB4\u734E\u52F5[/size][/color]\uFF0C\u66F4\u591A\u8AAA\u660E\u8ACB\u7FFB\u95B1\u6D3B\u52D5\u898F\u5247\u5662~"
    offset: 
    delay: 0
    param1: 
