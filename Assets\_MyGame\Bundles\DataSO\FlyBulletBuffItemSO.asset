%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: FlyBulletBuffItemSO
  m_EditorClassIdentifier: Assembly-CSharp::TableSO/FlyBulletBuffItemSo
  datas:
  - uniqueKey: 1
  - uniqueKey: 2
  - uniqueKey: 3
  - uniqueKey: 4
  - uniqueKey: 5
  - uniqueKey: 6
  - uniqueKey: 7
  - uniqueKey: 8
  - uniqueKey: 21
  - uniqueKey: 22
  - uniqueKey: 23
  - uniqueKey: 24
  - uniqueKey: 25
  - uniqueKey: 26
  - uniqueKey: 27
  - uniqueKey: 28
  - uniqueKey: 29
  - uniqueKey: 41
  - uniqueKey: 42
  - uniqueKey: 43
  - uniqueKey: 44
  - uniqueKey: 45
  - uniqueKey: 46
  - uniqueKey: 47
  - uniqueKey: 48
  - uniqueKey: 49
  - uniqueKey: 50
  - uniqueKey: 61
  - uniqueKey: 62
  - uniqueKey: 63
  - uniqueKey: 64
  - uniqueKey: 65
  - uniqueKey: 66
  - uniqueKey: 67
  - uniqueKey: 68
  - uniqueKey: 69
  - uniqueKey: 70
  - uniqueKey: 71
  - uniqueKey: 81
  - uniqueKey: 82
  - uniqueKey: 83
  - uniqueKey: 84
  - uniqueKey: 85
  - uniqueKey: 86
  - uniqueKey: 87
  - uniqueKey: 88
  - uniqueKey: 89
  - uniqueKey: 90
  - uniqueKey: 91
  - uniqueKey: 92
  - uniqueKey: 93
  - uniqueKey: 101
  - uniqueKey: 102
  - uniqueKey: 103
  - uniqueKey: 104
  - uniqueKey: 105
  - uniqueKey: 106
  - uniqueKey: 107
  - uniqueKey: 108
  - uniqueKey: 109
  - uniqueKey: 110
  - uniqueKey: 111
  - uniqueKey: 112
  - uniqueKey: 113
  - uniqueKey: 114
  - uniqueKey: 115
  - uniqueKey: 116
  - uniqueKey: 201
  - uniqueKey: 202
  - uniqueKey: 203
  - uniqueKey: 204
  - uniqueKey: 205
  - uniqueKey: 206
  - uniqueKey: 207
  - uniqueKey: 208
  - uniqueKey: 209
  - uniqueKey: 210
  - uniqueKey: 211
  - uniqueKey: 212
  - uniqueKey: 213
  - uniqueKey: 214
  - uniqueKey: 215
  - uniqueKey: 216
  - uniqueKey: 301
  - uniqueKey: 302
  - uniqueKey: 303
  - uniqueKey: 304
  - uniqueKey: 305
  - uniqueKey: 306
  - uniqueKey: 307
  - uniqueKey: 308
  - uniqueKey: 309
  - uniqueKey: 310
  - uniqueKey: 311
  - uniqueKey: 312
  - uniqueKey: 313
  - uniqueKey: 314
  - uniqueKey: 315
  - uniqueKey: 316
  - uniqueKey: 401
  - uniqueKey: 402
  - uniqueKey: 403
  - uniqueKey: 404
  - uniqueKey: 405
  - uniqueKey: 406
  - uniqueKey: 407
  - uniqueKey: 408
  - uniqueKey: 409
  - uniqueKey: 410
  - uniqueKey: 411
  - uniqueKey: 412
  - uniqueKey: 413
  - uniqueKey: 414
  - uniqueKey: 415
  - uniqueKey: 416
  - uniqueKey: 501
  - uniqueKey: 502
  - uniqueKey: 503
  - uniqueKey: 504
  - uniqueKey: 505
  - uniqueKey: 506
  - uniqueKey: 507
  - uniqueKey: 508
  - uniqueKey: 509
  - uniqueKey: 510
  - uniqueKey: 511
  - uniqueKey: 512
  - uniqueKey: 513
  - uniqueKey: 514
  - uniqueKey: 515
  - uniqueKey: 516
  - uniqueKey: 601
  - uniqueKey: 602
  - uniqueKey: 603
  - uniqueKey: 604
  - uniqueKey: 605
  - uniqueKey: 606
  - uniqueKey: 607
  - uniqueKey: 608
  - uniqueKey: 609
  - uniqueKey: 610
  - uniqueKey: 611
  - uniqueKey: 612
  - uniqueKey: 613
  - uniqueKey: 614
  - uniqueKey: 615
  - uniqueKey: 616
  - uniqueKey: 701
  - uniqueKey: 702
  - uniqueKey: 703
  - uniqueKey: 704
  - uniqueKey: 705
  - uniqueKey: 706
  - uniqueKey: 707
  - uniqueKey: 708
  - uniqueKey: 709
  - uniqueKey: 710
  - uniqueKey: 711
  - uniqueKey: 712
  - uniqueKey: 713
  - uniqueKey: 714
  - uniqueKey: 715
  - uniqueKey: 716
  - uniqueKey: 801
  - uniqueKey: 802
  - uniqueKey: 803
  - uniqueKey: 804
  - uniqueKey: 805
  - uniqueKey: 806
  - uniqueKey: 807
  - uniqueKey: 808
  - uniqueKey: 809
  - uniqueKey: 810
  - uniqueKey: 811
  - uniqueKey: 812
  - uniqueKey: 813
  - uniqueKey: 814
  - uniqueKey: 815
  - uniqueKey: 816
  - uniqueKey: 901
  - uniqueKey: 902
  - uniqueKey: 903
  - uniqueKey: 904
  - uniqueKey: 905
  - uniqueKey: 906
  - uniqueKey: 907
  - uniqueKey: 908
  - uniqueKey: 909
  - uniqueKey: 910
  - uniqueKey: 911
  - uniqueKey: 912
  - uniqueKey: 913
  - uniqueKey: 914
  - uniqueKey: 915
  - uniqueKey: 916
  - uniqueKey: 1001
  - uniqueKey: 1002
  - uniqueKey: 1003
  - uniqueKey: 1004
  - uniqueKey: 1005
  - uniqueKey: 1006
  - uniqueKey: 1007
  - uniqueKey: 1008
  - uniqueKey: 1009
  - uniqueKey: 1010
  - uniqueKey: 1011
  - uniqueKey: 1012
  - uniqueKey: 1013
  - uniqueKey: 1014
  - uniqueKey: 1015
  - uniqueKey: 1016
  - uniqueKey: 1101
  - uniqueKey: 1102
  - uniqueKey: 1103
  - uniqueKey: 1104
  - uniqueKey: 1105
  - uniqueKey: 1106
  - uniqueKey: 1107
  - uniqueKey: 1108
  - uniqueKey: 1109
  - uniqueKey: 1110
  - uniqueKey: 1111
  - uniqueKey: 1112
  - uniqueKey: 1113
  - uniqueKey: 1114
  - uniqueKey: 1115
  - uniqueKey: 1116
  - uniqueKey: 1201
  - uniqueKey: 1202
  - uniqueKey: 1203
  - uniqueKey: 1204
  - uniqueKey: 1205
  - uniqueKey: 1206
  - uniqueKey: 1207
  - uniqueKey: 1208
  - uniqueKey: 1209
  - uniqueKey: 1210
  - uniqueKey: 1211
  - uniqueKey: 1212
  - uniqueKey: 1213
  - uniqueKey: 1214
  - uniqueKey: 1215
  - uniqueKey: 1216
  - uniqueKey: 1301
  - uniqueKey: 1302
  - uniqueKey: 1303
  - uniqueKey: 1304
  - uniqueKey: 1305
  - uniqueKey: 1306
  - uniqueKey: 1307
  - uniqueKey: 1308
  - uniqueKey: 1309
  - uniqueKey: 1310
  - uniqueKey: 1311
  - uniqueKey: 1312
  - uniqueKey: 1313
  - uniqueKey: 1314
  - uniqueKey: 1315
  - uniqueKey: 1316
  - uniqueKey: 1401
  - uniqueKey: 1402
  - uniqueKey: 1403
  - uniqueKey: 1404
  - uniqueKey: 1405
  - uniqueKey: 1406
  - uniqueKey: 1407
  - uniqueKey: 1408
  - uniqueKey: 1409
  - uniqueKey: 1410
  - uniqueKey: 1411
  - uniqueKey: 1412
  - uniqueKey: 1413
  - uniqueKey: 1414
  - uniqueKey: 1415
  - uniqueKey: 1416
  - uniqueKey: 1501
  - uniqueKey: 1502
  - uniqueKey: 1503
  - uniqueKey: 1504
  - uniqueKey: 1505
  - uniqueKey: 1506
  - uniqueKey: 1507
  - uniqueKey: 1508
  - uniqueKey: 1509
  - uniqueKey: 1510
  - uniqueKey: 1511
  - uniqueKey: 1512
  - uniqueKey: 1513
  - uniqueKey: 1514
  - uniqueKey: 1515
  - uniqueKey: 1516
  - uniqueKey: 1601
  - uniqueKey: 1602
  - uniqueKey: 1603
  - uniqueKey: 1604
  - uniqueKey: 1605
  - uniqueKey: 1606
  - uniqueKey: 1607
  - uniqueKey: 1608
  - uniqueKey: 1609
  - uniqueKey: 1610
  - uniqueKey: 1611
  - uniqueKey: 1612
  - uniqueKey: 1613
  - uniqueKey: 1614
  - uniqueKey: 1615
  - uniqueKey: 1616
  - uniqueKey: 1701
  - uniqueKey: 1702
  - uniqueKey: 1703
  - uniqueKey: 1704
  - uniqueKey: 1705
  - uniqueKey: 1706
  - uniqueKey: 1707
  - uniqueKey: 1708
  - uniqueKey: 1709
  - uniqueKey: 1710
  - uniqueKey: 1711
  - uniqueKey: 1712
  - uniqueKey: 1713
  - uniqueKey: 1714
  - uniqueKey: 1715
  - uniqueKey: 1716
  - uniqueKey: 1801
  - uniqueKey: 1802
  - uniqueKey: 1803
  - uniqueKey: 1804
  - uniqueKey: 1805
  - uniqueKey: 1806
  - uniqueKey: 1807
  - uniqueKey: 1808
  - uniqueKey: 1809
  - uniqueKey: 1810
  - uniqueKey: 1811
  - uniqueKey: 1812
  - uniqueKey: 1813
  - uniqueKey: 1814
  - uniqueKey: 1815
  - uniqueKey: 1816
  - uniqueKey: 1901
  - uniqueKey: 1902
  - uniqueKey: 1903
  - uniqueKey: 1904
  - uniqueKey: 1905
  - uniqueKey: 1906
  - uniqueKey: 1907
  - uniqueKey: 1908
  - uniqueKey: 1909
  - uniqueKey: 1910
  - uniqueKey: 1911
  - uniqueKey: 1912
  - uniqueKey: 1913
  - uniqueKey: 1914
  - uniqueKey: 1915
  - uniqueKey: 1916
  - uniqueKey: 2001
  - uniqueKey: 2002
  - uniqueKey: 2003
  - uniqueKey: 2004
  - uniqueKey: 2005
  - uniqueKey: 2006
  - uniqueKey: 2007
  - uniqueKey: 2008
  - uniqueKey: 2009
  - uniqueKey: 2010
  - uniqueKey: 2011
  - uniqueKey: 2012
  - uniqueKey: 2013
  - uniqueKey: 2014
  - uniqueKey: 2015
  - uniqueKey: 2016
  datas: []
