%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a274c4e4c4a7c84980165c0b068fd0e, type: 3}
  m_Name: BattleGuideSO
  m_EditorClassIdentifier: 
  _datas:
  - uniqueKey: 1
    id: 1
    gateId: 1000
    time: 40
    heroInfo: 
    guideId: 30000
    panel: 
    step: 1
    passType: 1|4
  - uniqueKey: 2
    id: 2
    gateId: 1000
    time: 40
    heroInfo: 21|6|12100_12105_121000_121001_121002_121003_121004_121005
    guideId: 0
    panel: 
    step: 2
    passType: 1|4
  - uniqueKey: 3
    id: 3
    gateId: 1000
    time: 40
    heroInfo: 24|6|12400_12405_124000_124001_124002_124003_124004_124005
    guideId: 0
    panel: 
    step: 2
    passType: 1|4
  - uniqueKey: 4
    id: 4
    gateId: 1000
    time: 40
    heroInfo: 33|6|13300_13305_133000_133001_133002_133003_133004_133005
    guideId: 0
    panel: 
    step: 2
    passType: 1|4
  - uniqueKey: 100
    id: 100
    gateId: 1002
    time: 1
    heroInfo: 
    guideId: 30001
    panel: 
    step: 1
    passType: 4
