%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: BulletSO
  m_EditorClassIdentifier: Assembly-CSharp::TableSO/BulletSo
  datas:
  - uniqueKey: 101000
  - uniqueKey: 101010
  - uniqueKey: 101020
  - uniqueKey: 101030
  - uniqueKey: 101040
  - uniqueKey: 101050
  - uniqueKey: 102000
  - uniqueKey: 102010
  - uniqueKey: 102020
  - uniqueKey: 102030
  - uniqueKey: 102040
  - uniqueKey: 102050
  - uniqueKey: 102001
  - uniqueKey: 102011
  - uniqueKey: 102021
  - uniqueKey: 102031
  - uniqueKey: 102041
  - uniqueKey: 102051
  - uniqueKey: 103000
  - uniqueKey: 103010
  - uniqueKey: 103020
  - uniqueKey: 103030
  - uniqueKey: 103040
  - uniqueKey: 103050
  - uniqueKey: 103001
  - uniqueKey: 103011
  - uniqueKey: 103021
  - uniqueKey: 103031
  - uniqueKey: 103041
  - uniqueKey: 103051
  - uniqueKey: 103002
  - uniqueKey: 104000
  - uniqueKey: 104010
  - uniqueKey: 104020
  - uniqueKey: 104030
  - uniqueKey: 104040
  - uniqueKey: 104050
  - uniqueKey: 104001
  - uniqueKey: 104011
  - uniqueKey: 104021
  - uniqueKey: 104031
  - uniqueKey: 104041
  - uniqueKey: 104051
  - uniqueKey: 105000
  - uniqueKey: 105010
  - uniqueKey: 105020
  - uniqueKey: 105030
  - uniqueKey: 105040
  - uniqueKey: 105050
  - uniqueKey: 106000
  - uniqueKey: 106010
  - uniqueKey: 106020
  - uniqueKey: 106030
  - uniqueKey: 106040
  - uniqueKey: 106050
  - uniqueKey: 107000
  - uniqueKey: 107010
  - uniqueKey: 107020
  - uniqueKey: 107030
  - uniqueKey: 107040
  - uniqueKey: 107050
  - uniqueKey: 107001
  - uniqueKey: 107011
  - uniqueKey: 107021
  - uniqueKey: 107031
  - uniqueKey: 107041
  - uniqueKey: 107051
  - uniqueKey: 108000
  - uniqueKey: 108010
  - uniqueKey: 108020
  - uniqueKey: 108030
  - uniqueKey: 108040
  - uniqueKey: 108050
  - uniqueKey: 109000
  - uniqueKey: 109010
  - uniqueKey: 109020
  - uniqueKey: 109030
  - uniqueKey: 109040
  - uniqueKey: 109050
  - uniqueKey: 110000
  - uniqueKey: 110010
  - uniqueKey: 110020
  - uniqueKey: 110030
  - uniqueKey: 110040
  - uniqueKey: 110050
  - uniqueKey: 111000
  - uniqueKey: 111010
  - uniqueKey: 111020
  - uniqueKey: 111030
  - uniqueKey: 111040
  - uniqueKey: 111050
  - uniqueKey: 112000
  - uniqueKey: 112010
  - uniqueKey: 112020
  - uniqueKey: 112030
  - uniqueKey: 112040
  - uniqueKey: 112050
  - uniqueKey: 113000
  - uniqueKey: 113010
  - uniqueKey: 113020
  - uniqueKey: 113030
  - uniqueKey: 113040
  - uniqueKey: 113050
  - uniqueKey: 114000
  - uniqueKey: 114010
  - uniqueKey: 114020
  - uniqueKey: 114030
  - uniqueKey: 114040
  - uniqueKey: 114050
  - uniqueKey: 115000
  - uniqueKey: 115010
  - uniqueKey: 115020
  - uniqueKey: 115030
  - uniqueKey: 115040
  - uniqueKey: 115050
  - uniqueKey: 116000
  - uniqueKey: 116010
  - uniqueKey: 116020
  - uniqueKey: 116030
  - uniqueKey: 116040
  - uniqueKey: 116050
  - uniqueKey: 117000
  - uniqueKey: 117010
  - uniqueKey: 117020
  - uniqueKey: 117030
  - uniqueKey: 117040
  - uniqueKey: 117050
  - uniqueKey: 117001
  - uniqueKey: 118000
  - uniqueKey: 118010
  - uniqueKey: 118020
  - uniqueKey: 118030
  - uniqueKey: 118040
  - uniqueKey: 118050
  - uniqueKey: 119000
  - uniqueKey: 119010
  - uniqueKey: 119020
  - uniqueKey: 119030
  - uniqueKey: 119040
  - uniqueKey: 119050
  - uniqueKey: 119001
  - uniqueKey: 119011
  - uniqueKey: 119021
  - uniqueKey: 119031
  - uniqueKey: 119041
  - uniqueKey: 119051
  - uniqueKey: 119002
  - uniqueKey: 120000
  - uniqueKey: 120010
  - uniqueKey: 120020
  - uniqueKey: 120030
  - uniqueKey: 120040
  - uniqueKey: 120050
  - uniqueKey: 121000
  - uniqueKey: 121010
  - uniqueKey: 121020
  - uniqueKey: 121030
  - uniqueKey: 121040
  - uniqueKey: 121050
  - uniqueKey: 121001
  - uniqueKey: 121011
  - uniqueKey: 121021
  - uniqueKey: 121031
  - uniqueKey: 121041
  - uniqueKey: 121051
  - uniqueKey: 122000
  - uniqueKey: 122010
  - uniqueKey: 122020
  - uniqueKey: 122030
  - uniqueKey: 122040
  - uniqueKey: 122050
  - uniqueKey: 123000
  - uniqueKey: 123010
  - uniqueKey: 123020
  - uniqueKey: 123030
  - uniqueKey: 123040
  - uniqueKey: 123050
  - uniqueKey: 124000
  - uniqueKey: 124010
  - uniqueKey: 124020
  - uniqueKey: 124030
  - uniqueKey: 124040
  - uniqueKey: 124050
  - uniqueKey: 125000
  - uniqueKey: 125010
  - uniqueKey: 125020
  - uniqueKey: 125030
  - uniqueKey: 125040
  - uniqueKey: 125050
  - uniqueKey: 125001
  - uniqueKey: 126000
  - uniqueKey: 126010
  - uniqueKey: 126020
  - uniqueKey: 126030
  - uniqueKey: 126040
  - uniqueKey: 126050
  - uniqueKey: 127000
  - uniqueKey: 127010
  - uniqueKey: 127020
  - uniqueKey: 127030
  - uniqueKey: 127040
  - uniqueKey: 127050
  - uniqueKey: 127001
  - uniqueKey: 127011
  - uniqueKey: 127021
  - uniqueKey: 127031
  - uniqueKey: 127041
  - uniqueKey: 127051
  - uniqueKey: 128000
  - uniqueKey: 128010
  - uniqueKey: 128020
  - uniqueKey: 128030
  - uniqueKey: 128040
  - uniqueKey: 128050
  - uniqueKey: 128001
  - uniqueKey: 128011
  - uniqueKey: 128021
  - uniqueKey: 128031
  - uniqueKey: 128041
  - uniqueKey: 128051
  - uniqueKey: 129000
  - uniqueKey: 129010
  - uniqueKey: 129020
  - uniqueKey: 129030
  - uniqueKey: 129040
  - uniqueKey: 129050
  - uniqueKey: 130000
  - uniqueKey: 130010
  - uniqueKey: 130020
  - uniqueKey: 130030
  - uniqueKey: 130040
  - uniqueKey: 130050
  - uniqueKey: 130001
  - uniqueKey: 130011
  - uniqueKey: 130021
  - uniqueKey: 130031
  - uniqueKey: 130041
  - uniqueKey: 130051
  - uniqueKey: 131000
  - uniqueKey: 131010
  - uniqueKey: 131020
  - uniqueKey: 131030
  - uniqueKey: 131040
  - uniqueKey: 131050
  - uniqueKey: 132000
  - uniqueKey: 132010
  - uniqueKey: 132020
  - uniqueKey: 132030
  - uniqueKey: 132040
  - uniqueKey: 132050
  - uniqueKey: 132001
  - uniqueKey: 132011
  - uniqueKey: 132021
  - uniqueKey: 132031
  - uniqueKey: 132041
  - uniqueKey: 132051
  - uniqueKey: 132002
  - uniqueKey: 132012
  - uniqueKey: 133000
  - uniqueKey: 133010
  - uniqueKey: 133020
  - uniqueKey: 133030
  - uniqueKey: 133040
  - uniqueKey: 133050
  - uniqueKey: 134000
  - uniqueKey: 134010
  - uniqueKey: 134020
  - uniqueKey: 134030
  - uniqueKey: 134040
  - uniqueKey: 134050
  - uniqueKey: 135000
  - uniqueKey: 135010
  - uniqueKey: 135020
  - uniqueKey: 135030
  - uniqueKey: 135040
  - uniqueKey: 135050
  - uniqueKey: 136000
  - uniqueKey: 136010
  - uniqueKey: 136020
  - uniqueKey: 136030
  - uniqueKey: 136040
  - uniqueKey: 136050
  - uniqueKey: 137000
  - uniqueKey: 137010
  - uniqueKey: 137020
  - uniqueKey: 137030
  - uniqueKey: 137040
  - uniqueKey: 137050
  - uniqueKey: 138000
  - uniqueKey: 138010
  - uniqueKey: 138020
  - uniqueKey: 138030
  - uniqueKey: 138040
  - uniqueKey: 138050
  - uniqueKey: 138001
  - uniqueKey: 138011
  - uniqueKey: 138021
  - uniqueKey: 138031
  - uniqueKey: 138041
  - uniqueKey: 138051
  - uniqueKey: 138002
  - uniqueKey: 138012
  - uniqueKey: 138022
  - uniqueKey: 138032
  - uniqueKey: 138042
  - uniqueKey: 138052
  - uniqueKey: 139000
  - uniqueKey: 139010
  - uniqueKey: 139020
  - uniqueKey: 139030
  - uniqueKey: 139040
  - uniqueKey: 139050
  - uniqueKey: 139001
  - uniqueKey: 139011
  - uniqueKey: 139021
  - uniqueKey: 139031
  - uniqueKey: 139041
  - uniqueKey: 139051
  - uniqueKey: 140000
  - uniqueKey: 140010
  - uniqueKey: 140020
  - uniqueKey: 140030
  - uniqueKey: 140040
  - uniqueKey: 140050
  - uniqueKey: 141000
  - uniqueKey: 141010
  - uniqueKey: 141020
  - uniqueKey: 141030
  - uniqueKey: 141040
  - uniqueKey: 141050
  - uniqueKey: 1100000
  - uniqueKey: 1100010
  - uniqueKey: 1100020
  - uniqueKey: 1100030
  - uniqueKey: 1100040
  - uniqueKey: 1100050
  - uniqueKey: 1100001
  - uniqueKey: 1100011
  - uniqueKey: 1100021
  - uniqueKey: 1100031
  - uniqueKey: 1100041
  - uniqueKey: 1100051
  - uniqueKey: 1100002
  - uniqueKey: 1100012
  - uniqueKey: 1100022
  - uniqueKey: 1100032
  - uniqueKey: 1100042
  - uniqueKey: 1100052
  - uniqueKey: 1100100
  - uniqueKey: 1100101
  - uniqueKey: 1100102
  - uniqueKey: 1100103
  - uniqueKey: 1100104
  - uniqueKey: 1100105
  - uniqueKey: 1101000
  - uniqueKey: 1101010
  - uniqueKey: 1101020
  - uniqueKey: 1101030
  - uniqueKey: 1101040
  - uniqueKey: 1101050
  - uniqueKey: 1101100
  - uniqueKey: 1101101
  - uniqueKey: 1101102
  - uniqueKey: 1101103
  - uniqueKey: 1101104
  - uniqueKey: 1101105
  - uniqueKey: 1102000
  - uniqueKey: 1102010
  - uniqueKey: 1102020
  - uniqueKey: 1102030
  - uniqueKey: 1102040
  - uniqueKey: 1102050
  - uniqueKey: 1102100
  - uniqueKey: 1102101
  - uniqueKey: 1102102
  - uniqueKey: 1102103
  - uniqueKey: 1102104
  - uniqueKey: 1102105
  - uniqueKey: 1103000
  - uniqueKey: 1103010
  - uniqueKey: 1103020
  - uniqueKey: 1103030
  - uniqueKey: 1103040
  - uniqueKey: 1103050
  - uniqueKey: 1103100
  - uniqueKey: 1103101
  - uniqueKey: 1103102
  - uniqueKey: 1103103
  - uniqueKey: 1103104
  - uniqueKey: 1103105
  - uniqueKey: 1104000
  - uniqueKey: 1104010
  - uniqueKey: 1104020
  - uniqueKey: 1104030
  - uniqueKey: 1104040
  - uniqueKey: 1104050
  - uniqueKey: 1104100
  - uniqueKey: 1104101
  - uniqueKey: 1104102
  - uniqueKey: 1104103
  - uniqueKey: 1104104
  - uniqueKey: 1104105
  - uniqueKey: 1105000
  - uniqueKey: 1105010
  - uniqueKey: 1105020
  - uniqueKey: 1105030
  - uniqueKey: 1105040
  - uniqueKey: 1105050
  - uniqueKey: 1105100
  - uniqueKey: 1105101
  - uniqueKey: 1105102
  - uniqueKey: 1105103
  - uniqueKey: 1105104
  - uniqueKey: 1105105
  - uniqueKey: 1105110
  - uniqueKey: 1105111
  - uniqueKey: 1105112
  - uniqueKey: 1105113
  - uniqueKey: 1105114
  - uniqueKey: 1105115
  - uniqueKey: 1106000
  - uniqueKey: 1106010
  - uniqueKey: 1106020
  - uniqueKey: 1106030
  - uniqueKey: 1106040
  - uniqueKey: 1106050
  - uniqueKey: 1106100
  - uniqueKey: 1106101
  - uniqueKey: 1106102
  - uniqueKey: 1106103
  - uniqueKey: 1106104
  - uniqueKey: 1106105
  - uniqueKey: 1107000
  - uniqueKey: 1107010
  - uniqueKey: 1107020
  - uniqueKey: 1107030
  - uniqueKey: 1107040
  - uniqueKey: 1107050
  - uniqueKey: 1107100
  - uniqueKey: 1107101
  - uniqueKey: 1107102
  - uniqueKey: 1107103
  - uniqueKey: 1107104
  - uniqueKey: 1107105
  - uniqueKey: 1108000
  - uniqueKey: 1108010
  - uniqueKey: 1108020
  - uniqueKey: 1108030
  - uniqueKey: 1108040
  - uniqueKey: 1108050
  - uniqueKey: 1108100
  - uniqueKey: 1108101
  - uniqueKey: 1108102
  - uniqueKey: 1108103
  - uniqueKey: 1108104
  - uniqueKey: 1108105
  - uniqueKey: 1108110
  - uniqueKey: 1108111
  - uniqueKey: 1108112
  - uniqueKey: 1108113
  - uniqueKey: 1108114
  - uniqueKey: 1108115
  - uniqueKey: 1109000
  - uniqueKey: 1109010
  - uniqueKey: 1109020
  - uniqueKey: 1109030
  - uniqueKey: 1109040
  - uniqueKey: 1109050
  - uniqueKey: 1109100
  - uniqueKey: 1109101
  - uniqueKey: 1109102
  - uniqueKey: 1109103
  - uniqueKey: 1109104
  - uniqueKey: 1109105
  - uniqueKey: 1110000
  - uniqueKey: 1110010
  - uniqueKey: 1110020
  - uniqueKey: 1110030
  - uniqueKey: 1110040
  - uniqueKey: 1110050
  - uniqueKey: 1110100
  - uniqueKey: 1110101
  - uniqueKey: 1110102
  - uniqueKey: 1110103
  - uniqueKey: 1110104
  - uniqueKey: 1110105
  - uniqueKey: 1111000
  - uniqueKey: 1111010
  - uniqueKey: 1111020
  - uniqueKey: 1111030
  - uniqueKey: 1111040
  - uniqueKey: 1111050
  - uniqueKey: 1111001
  - uniqueKey: 1111011
  - uniqueKey: 1111021
  - uniqueKey: 1111031
  - uniqueKey: 1111041
  - uniqueKey: 1111051
  - uniqueKey: 1111100
  - uniqueKey: 1111101
  - uniqueKey: 1111102
  - uniqueKey: 1111103
  - uniqueKey: 1111104
  - uniqueKey: 1111105
  - uniqueKey: 82000
  - uniqueKey: 82001
  - uniqueKey: 82002
  - uniqueKey: 82003
  - uniqueKey: 82004
  - uniqueKey: 82005
  - uniqueKey: 200000
  - uniqueKey: 200010
  - uniqueKey: 200020
  - uniqueKey: 200030
  - uniqueKey: 200040
  - uniqueKey: 210000
  - uniqueKey: 210010
  - uniqueKey: 210020
  - uniqueKey: 210030
  - uniqueKey: 210040
  - uniqueKey: 220000
  - uniqueKey: 220001
  - uniqueKey: 220002
  - uniqueKey: 220003
  - uniqueKey: 220004
  - uniqueKey: 220005
  - uniqueKey: 220006
  - uniqueKey: 220007
  - uniqueKey: 220008
  - uniqueKey: 220009
  - uniqueKey: 220011
  - uniqueKey: 301000
  - uniqueKey: 301010
  - uniqueKey: 301020
  - uniqueKey: 301030
  - uniqueKey: 301040
  - uniqueKey: 301050
  - uniqueKey: 301060
  - uniqueKey: 301070
  - uniqueKey: 301080
  - uniqueKey: 301090
  - uniqueKey: 302000
  - uniqueKey: 302010
  - uniqueKey: 302020
  - uniqueKey: 302030
  - uniqueKey: 302040
  - uniqueKey: 302050
  - uniqueKey: 302060
  - uniqueKey: 302070
  - uniqueKey: 302080
  - uniqueKey: 302090
  - uniqueKey: 303000
  - uniqueKey: 303010
  - uniqueKey: 303020
  - uniqueKey: 303030
  - uniqueKey: 303040
  - uniqueKey: 303050
  - uniqueKey: 303060
  - uniqueKey: 303070
  - uniqueKey: 303080
  - uniqueKey: 303090
  - uniqueKey: 304000
  - uniqueKey: 304010
  - uniqueKey: 304020
  - uniqueKey: 304030
  - uniqueKey: 304040
  - uniqueKey: 304050
  - uniqueKey: 304060
  - uniqueKey: 304070
  - uniqueKey: 304080
  - uniqueKey: 304090
  - uniqueKey: 304001
  - uniqueKey: 304011
  - uniqueKey: 304021
  - uniqueKey: 304031
  - uniqueKey: 304041
  - uniqueKey: 304051
  - uniqueKey: 304061
  - uniqueKey: 304071
  - uniqueKey: 304081
  - uniqueKey: 304091
  - uniqueKey: 305000
  - uniqueKey: 305010
  - uniqueKey: 305020
  - uniqueKey: 305030
  - uniqueKey: 305040
  - uniqueKey: 305050
  - uniqueKey: 305060
  - uniqueKey: 305070
  - uniqueKey: 305080
  - uniqueKey: 305090
  - uniqueKey: 401000
  - uniqueKey: 401010
  - uniqueKey: 401020
  - uniqueKey: 401030
  - uniqueKey: 401040
  - uniqueKey: 401050
  - uniqueKey: 401060
  - uniqueKey: 401070
  - uniqueKey: 401080
  - uniqueKey: 401090
  - uniqueKey: 402000
  - uniqueKey: 402010
  - uniqueKey: 402020
  - uniqueKey: 402030
  - uniqueKey: 402040
  - uniqueKey: 402050
  - uniqueKey: 402060
  - uniqueKey: 402070
  - uniqueKey: 402080
  - uniqueKey: 402090
  - uniqueKey: 403000
  - uniqueKey: 403010
  - uniqueKey: 403020
  - uniqueKey: 403030
  - uniqueKey: 403040
  - uniqueKey: 403050
  - uniqueKey: 403060
  - uniqueKey: 403070
  - uniqueKey: 403080
  - uniqueKey: 403090
  - uniqueKey: 404000
  - uniqueKey: 404010
  - uniqueKey: 404020
  - uniqueKey: 404030
  - uniqueKey: 404040
  - uniqueKey: 404050
  - uniqueKey: 404060
  - uniqueKey: 404070
  - uniqueKey: 404080
  - uniqueKey: 404090
  - uniqueKey: 60000
  - uniqueKey: 60001
  - uniqueKey: 60002
  - uniqueKey: 61000
  - uniqueKey: 61001
  - uniqueKey: 61101
  - uniqueKey: 61102
  - uniqueKey: 61103
  - uniqueKey: 62000
  - uniqueKey: 62001
  - uniqueKey: 62002
  - uniqueKey: 62003
  - uniqueKey: 62004
  - uniqueKey: 62005
  - uniqueKey: 62100
  - uniqueKey: 62101
  - uniqueKey: 62102
  - uniqueKey: 63000
  - uniqueKey: 63001
  - uniqueKey: 63002
  - uniqueKey: 63003
  - uniqueKey: 63004
  - uniqueKey: 63005
  - uniqueKey: 63100
  - uniqueKey: 63101
  - uniqueKey: 63102
  - uniqueKey: 64000
  - uniqueKey: 64001
  - uniqueKey: 64002
  - uniqueKey: 64003
  - uniqueKey: 64100
  - uniqueKey: 65000
  - uniqueKey: 65001
  - uniqueKey: 65002
  - uniqueKey: 65003
  - uniqueKey: 65100
  - uniqueKey: 66000
  - uniqueKey: 66001
  - uniqueKey: 66100
  - uniqueKey: 67000
  - uniqueKey: 67001
  - uniqueKey: 67002
  - uniqueKey: 67003
  - uniqueKey: 67004
  - uniqueKey: 67005
  - uniqueKey: 67100
  - uniqueKey: 67101
  - uniqueKey: 67102
  - uniqueKey: 68000
  - uniqueKey: 68001
  - uniqueKey: 68002
  - uniqueKey: 68003
  - uniqueKey: 68004
  - uniqueKey: 68005
  - uniqueKey: 68100
  - uniqueKey: 68101
  - uniqueKey: 68102
  - uniqueKey: 69000
  - uniqueKey: 69001
  - uniqueKey: 69002
  - uniqueKey: 69010
  - uniqueKey: 69100
  - uniqueKey: 69101
  - uniqueKey: 69102
  - uniqueKey: 69110
  - uniqueKey: 70000
  - uniqueKey: 70010
  - uniqueKey: 70011
  - uniqueKey: 70012
  - uniqueKey: 70101
  - uniqueKey: 70102
  - uniqueKey: 70103
  - uniqueKey: 71000
  - uniqueKey: 71010
  - uniqueKey: 71011
  - uniqueKey: 71012
  - uniqueKey: 71100
  - uniqueKey: 71101
  - uniqueKey: 71102
  - uniqueKey: 72000
  - uniqueKey: 72010
  - uniqueKey: 72011
  - uniqueKey: 72012
  - uniqueKey: 72100
  - uniqueKey: 72101
  - uniqueKey: 72102
  - uniqueKey: 73000
  - uniqueKey: 73001
  - uniqueKey: 73002
  - uniqueKey: 73010
  - uniqueKey: 73100
  - uniqueKey: 73101
  - uniqueKey: 73102
  - uniqueKey: 74000
  - uniqueKey: 74001
  - uniqueKey: 74002
  - uniqueKey: 74010
  - uniqueKey: 74011
  - uniqueKey: 74012
  - uniqueKey: 74100
  - uniqueKey: 74101
  - uniqueKey: 74102
  datas: []
