%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: MidWayBuffItemSO
  m_EditorClassIdentifier: Assembly-CSharp::TableSO/MidWayBuffItemSo
  datas:
  - uniqueKey: 1
  - uniqueKey: 2
  - uniqueKey: 3
  - uniqueKey: 4
  - uniqueKey: 5
  - uniqueKey: 11
  - uniqueKey: 12
  - uniqueKey: 13
  - uniqueKey: 14
  - uniqueKey: 15
  - uniqueKey: 16
  - uniqueKey: 17
  - uniqueKey: 18
  - uniqueKey: 19
  - uniqueKey: 20
  - uniqueKey: 21
  - uniqueKey: 101
  - uniqueKey: 102
  - uniqueKey: 103
  - uniqueKey: 104
  - uniqueKey: 105
  - uniqueKey: 106
  - uniqueKey: 111
  - uniqueKey: 112
  - uniqueKey: 113
  - uniqueKey: 114
  - uniqueKey: 115
  - uniqueKey: 116
  - uniqueKey: 117
  - uniqueKey: 118
  - uniqueKey: 119
  - uniqueKey: 120
  - uniqueKey: 121
  - uniqueKey: 122
  - uniqueKey: 123
  - uniqueKey: 124
  - uniqueKey: 201
  - uniqueKey: 202
  - uniqueKey: 203
  - uniqueKey: 204
  - uniqueKey: 205
  - uniqueKey: 206
  - uniqueKey: 207
  - uniqueKey: 211
  - uniqueKey: 212
  - uniqueKey: 213
  - uniqueKey: 214
  - uniqueKey: 215
  - uniqueKey: 216
  - uniqueKey: 217
  - uniqueKey: 218
  - uniqueKey: 219
  - uniqueKey: 220
  - uniqueKey: 221
  - uniqueKey: 222
  - uniqueKey: 223
  - uniqueKey: 224
  - uniqueKey: 301
  - uniqueKey: 302
  - uniqueKey: 303
  - uniqueKey: 304
  - uniqueKey: 305
  - uniqueKey: 306
  - uniqueKey: 307
  - uniqueKey: 308
  - uniqueKey: 309
  - uniqueKey: 311
  - uniqueKey: 312
  - uniqueKey: 313
  - uniqueKey: 314
  - uniqueKey: 315
  - uniqueKey: 316
  - uniqueKey: 317
  - uniqueKey: 318
  - uniqueKey: 319
  - uniqueKey: 320
  - uniqueKey: 321
  - uniqueKey: 322
  - uniqueKey: 323
  - uniqueKey: 324
  - uniqueKey: 325
  - uniqueKey: 326
  - uniqueKey: 327
  - uniqueKey: 401
  - uniqueKey: 402
  - uniqueKey: 403
  - uniqueKey: 404
  - uniqueKey: 405
  - uniqueKey: 406
  - uniqueKey: 407
  - uniqueKey: 408
  - uniqueKey: 409
  - uniqueKey: 410
  - uniqueKey: 411
  - uniqueKey: 412
  - uniqueKey: 413
  - uniqueKey: 414
  - uniqueKey: 415
  - uniqueKey: 416
  - uniqueKey: 417
  - uniqueKey: 418
  - uniqueKey: 419
  - uniqueKey: 420
  - uniqueKey: 421
  - uniqueKey: 422
  - uniqueKey: 423
  - uniqueKey: 424
  - uniqueKey: 425
  - uniqueKey: 426
  - uniqueKey: 427
  - uniqueKey: 501
  - uniqueKey: 502
  - uniqueKey: 503
  - uniqueKey: 504
  - uniqueKey: 505
  - uniqueKey: 506
  - uniqueKey: 507
  - uniqueKey: 508
  - uniqueKey: 509
  - uniqueKey: 510
  - uniqueKey: 511
  - uniqueKey: 512
  - uniqueKey: 513
  - uniqueKey: 514
  - uniqueKey: 515
  - uniqueKey: 516
  - uniqueKey: 517
  - uniqueKey: 518
  - uniqueKey: 519
  - uniqueKey: 520
  - uniqueKey: 521
  - uniqueKey: 522
  - uniqueKey: 523
  - uniqueKey: 524
  - uniqueKey: 525
  - uniqueKey: 526
  - uniqueKey: 527
  - uniqueKey: 528
  - uniqueKey: 529
  - uniqueKey: 530
  - uniqueKey: 601
  - uniqueKey: 602
  - uniqueKey: 603
  - uniqueKey: 604
  - uniqueKey: 605
  - uniqueKey: 606
  - uniqueKey: 607
  - uniqueKey: 608
  - uniqueKey: 609
  - uniqueKey: 610
  - uniqueKey: 611
  - uniqueKey: 612
  - uniqueKey: 613
  - uniqueKey: 614
  - uniqueKey: 615
  - uniqueKey: 616
  - uniqueKey: 617
  - uniqueKey: 618
  - uniqueKey: 619
  - uniqueKey: 620
  - uniqueKey: 621
  - uniqueKey: 622
  - uniqueKey: 623
  - uniqueKey: 624
  - uniqueKey: 625
  - uniqueKey: 626
  - uniqueKey: 627
  - uniqueKey: 628
  - uniqueKey: 629
  - uniqueKey: 630
  - uniqueKey: 701
  - uniqueKey: 702
  - uniqueKey: 703
  - uniqueKey: 704
  - uniqueKey: 705
  - uniqueKey: 706
  - uniqueKey: 707
  - uniqueKey: 708
  - uniqueKey: 709
  - uniqueKey: 710
  - uniqueKey: 711
  - uniqueKey: 712
  - uniqueKey: 713
  - uniqueKey: 714
  - uniqueKey: 715
  - uniqueKey: 716
  - uniqueKey: 717
  - uniqueKey: 718
  - uniqueKey: 719
  - uniqueKey: 720
  - uniqueKey: 721
  - uniqueKey: 722
  - uniqueKey: 723
  - uniqueKey: 724
  - uniqueKey: 725
  - uniqueKey: 726
  - uniqueKey: 727
  - uniqueKey: 728
  - uniqueKey: 729
  - uniqueKey: 730
  - uniqueKey: 801
  - uniqueKey: 802
  - uniqueKey: 803
  - uniqueKey: 804
  - uniqueKey: 805
  - uniqueKey: 806
  - uniqueKey: 807
  - uniqueKey: 808
  - uniqueKey: 809
  - uniqueKey: 810
  - uniqueKey: 811
  - uniqueKey: 812
  - uniqueKey: 813
  - uniqueKey: 814
  - uniqueKey: 815
  - uniqueKey: 816
  - uniqueKey: 817
  - uniqueKey: 818
  - uniqueKey: 819
  - uniqueKey: 820
  - uniqueKey: 821
  - uniqueKey: 822
  - uniqueKey: 823
  - uniqueKey: 824
  - uniqueKey: 825
  - uniqueKey: 826
  - uniqueKey: 827
  - uniqueKey: 828
  - uniqueKey: 829
  - uniqueKey: 830
  - uniqueKey: 901
  - uniqueKey: 902
  - uniqueKey: 903
  - uniqueKey: 904
  - uniqueKey: 905
  - uniqueKey: 906
  - uniqueKey: 907
  - uniqueKey: 908
  - uniqueKey: 909
  - uniqueKey: 910
  - uniqueKey: 911
  - uniqueKey: 912
  - uniqueKey: 913
  - uniqueKey: 914
  - uniqueKey: 915
  - uniqueKey: 916
  - uniqueKey: 917
  - uniqueKey: 918
  - uniqueKey: 919
  - uniqueKey: 920
  - uniqueKey: 921
  - uniqueKey: 922
  - uniqueKey: 923
  - uniqueKey: 924
  - uniqueKey: 925
  - uniqueKey: 926
  - uniqueKey: 927
  - uniqueKey: 928
  - uniqueKey: 929
  - uniqueKey: 930
  - uniqueKey: 1001
  - uniqueKey: 1002
  - uniqueKey: 1003
  - uniqueKey: 1004
  - uniqueKey: 1005
  - uniqueKey: 1006
  - uniqueKey: 1007
  - uniqueKey: 1008
  - uniqueKey: 1009
  - uniqueKey: 1010
  - uniqueKey: 1011
  - uniqueKey: 1012
  - uniqueKey: 1013
  - uniqueKey: 1014
  - uniqueKey: 1015
  - uniqueKey: 1016
  - uniqueKey: 1017
  - uniqueKey: 1018
  - uniqueKey: 1019
  - uniqueKey: 1020
  - uniqueKey: 1021
  - uniqueKey: 1022
  - uniqueKey: 1023
  - uniqueKey: 1024
  - uniqueKey: 1025
  - uniqueKey: 1026
  - uniqueKey: 1027
  - uniqueKey: 1028
  - uniqueKey: 1029
  - uniqueKey: 1030
  - uniqueKey: 1101
  - uniqueKey: 1102
  - uniqueKey: 1103
  - uniqueKey: 1104
  - uniqueKey: 1105
  - uniqueKey: 1106
  - uniqueKey: 1107
  - uniqueKey: 1108
  - uniqueKey: 1109
  - uniqueKey: 1110
  - uniqueKey: 1111
  - uniqueKey: 1112
  - uniqueKey: 1113
  - uniqueKey: 1114
  - uniqueKey: 1115
  - uniqueKey: 1116
  - uniqueKey: 1117
  - uniqueKey: 1118
  - uniqueKey: 1119
  - uniqueKey: 1120
  - uniqueKey: 1121
  - uniqueKey: 1122
  - uniqueKey: 1123
  - uniqueKey: 1124
  - uniqueKey: 1125
  - uniqueKey: 1126
  - uniqueKey: 1127
  - uniqueKey: 1128
  - uniqueKey: 1129
  - uniqueKey: 1130
  - uniqueKey: 1201
  - uniqueKey: 1202
  - uniqueKey: 1203
  - uniqueKey: 1204
  - uniqueKey: 1205
  - uniqueKey: 1206
  - uniqueKey: 1207
  - uniqueKey: 1208
  - uniqueKey: 1209
  - uniqueKey: 1210
  - uniqueKey: 1211
  - uniqueKey: 1212
  - uniqueKey: 1213
  - uniqueKey: 1214
  - uniqueKey: 1215
  - uniqueKey: 1216
  - uniqueKey: 1217
  - uniqueKey: 1218
  - uniqueKey: 1219
  - uniqueKey: 1220
  - uniqueKey: 1221
  - uniqueKey: 1222
  - uniqueKey: 1223
  - uniqueKey: 1224
  - uniqueKey: 1225
  - uniqueKey: 1226
  - uniqueKey: 1227
  - uniqueKey: 1228
  - uniqueKey: 1229
  - uniqueKey: 1230
  - uniqueKey: 1301
  - uniqueKey: 1302
  - uniqueKey: 1303
  - uniqueKey: 1304
  - uniqueKey: 1305
  - uniqueKey: 1306
  - uniqueKey: 1307
  - uniqueKey: 1308
  - uniqueKey: 1309
  - uniqueKey: 1310
  - uniqueKey: 1311
  - uniqueKey: 1312
  - uniqueKey: 1313
  - uniqueKey: 1314
  - uniqueKey: 1315
  - uniqueKey: 1316
  - uniqueKey: 1317
  - uniqueKey: 1318
  - uniqueKey: 1319
  - uniqueKey: 1320
  - uniqueKey: 1321
  - uniqueKey: 1322
  - uniqueKey: 1323
  - uniqueKey: 1324
  - uniqueKey: 1325
  - uniqueKey: 1326
  - uniqueKey: 1327
  - uniqueKey: 1328
  - uniqueKey: 1329
  - uniqueKey: 1330
  - uniqueKey: 1401
  - uniqueKey: 1402
  - uniqueKey: 1403
  - uniqueKey: 1404
  - uniqueKey: 1405
  - uniqueKey: 1406
  - uniqueKey: 1407
  - uniqueKey: 1408
  - uniqueKey: 1409
  - uniqueKey: 1410
  - uniqueKey: 1411
  - uniqueKey: 1412
  - uniqueKey: 1413
  - uniqueKey: 1414
  - uniqueKey: 1415
  - uniqueKey: 1416
  - uniqueKey: 1417
  - uniqueKey: 1418
  - uniqueKey: 1419
  - uniqueKey: 1420
  - uniqueKey: 1421
  - uniqueKey: 1422
  - uniqueKey: 1423
  - uniqueKey: 1424
  - uniqueKey: 1425
  - uniqueKey: 1426
  - uniqueKey: 1427
  - uniqueKey: 1428
  - uniqueKey: 1429
  - uniqueKey: 1430
  - uniqueKey: 1501
  - uniqueKey: 1502
  - uniqueKey: 1503
  - uniqueKey: 1504
  - uniqueKey: 1505
  - uniqueKey: 1506
  - uniqueKey: 1507
  - uniqueKey: 1508
  - uniqueKey: 1509
  - uniqueKey: 1510
  - uniqueKey: 1511
  - uniqueKey: 1512
  - uniqueKey: 1513
  - uniqueKey: 1514
  - uniqueKey: 1515
  - uniqueKey: 1516
  - uniqueKey: 1517
  - uniqueKey: 1518
  - uniqueKey: 1519
  - uniqueKey: 1520
  - uniqueKey: 1521
  - uniqueKey: 1522
  - uniqueKey: 1523
  - uniqueKey: 1524
  - uniqueKey: 1525
  - uniqueKey: 1526
  - uniqueKey: 1527
  - uniqueKey: 1528
  - uniqueKey: 1529
  - uniqueKey: 1530
  - uniqueKey: 1601
  - uniqueKey: 1602
  - uniqueKey: 1603
  - uniqueKey: 1604
  - uniqueKey: 1605
  - uniqueKey: 1606
  - uniqueKey: 1607
  - uniqueKey: 1608
  - uniqueKey: 1609
  - uniqueKey: 1610
  - uniqueKey: 1611
  - uniqueKey: 1612
  - uniqueKey: 1613
  - uniqueKey: 1614
  - uniqueKey: 1615
  - uniqueKey: 1616
  - uniqueKey: 1617
  - uniqueKey: 1618
  - uniqueKey: 1619
  - uniqueKey: 1620
  - uniqueKey: 1621
  - uniqueKey: 1622
  - uniqueKey: 1623
  - uniqueKey: 1624
  - uniqueKey: 1625
  - uniqueKey: 1626
  - uniqueKey: 1627
  - uniqueKey: 1628
  - uniqueKey: 1629
  - uniqueKey: 1630
  - uniqueKey: 1701
  - uniqueKey: 1702
  - uniqueKey: 1703
  - uniqueKey: 1704
  - uniqueKey: 1705
  - uniqueKey: 1706
  - uniqueKey: 1707
  - uniqueKey: 1708
  - uniqueKey: 1709
  - uniqueKey: 1710
  - uniqueKey: 1711
  - uniqueKey: 1712
  - uniqueKey: 1713
  - uniqueKey: 1714
  - uniqueKey: 1715
  - uniqueKey: 1716
  - uniqueKey: 1717
  - uniqueKey: 1718
  - uniqueKey: 1719
  - uniqueKey: 1720
  - uniqueKey: 1721
  - uniqueKey: 1722
  - uniqueKey: 1723
  - uniqueKey: 1724
  - uniqueKey: 1725
  - uniqueKey: 1726
  - uniqueKey: 1727
  - uniqueKey: 1728
  - uniqueKey: 1729
  - uniqueKey: 1730
  - uniqueKey: 1801
  - uniqueKey: 1802
  - uniqueKey: 1803
  - uniqueKey: 1804
  - uniqueKey: 1805
  - uniqueKey: 1806
  - uniqueKey: 1807
  - uniqueKey: 1808
  - uniqueKey: 1809
  - uniqueKey: 1810
  - uniqueKey: 1811
  - uniqueKey: 1812
  - uniqueKey: 1813
  - uniqueKey: 1814
  - uniqueKey: 1815
  - uniqueKey: 1816
  - uniqueKey: 1817
  - uniqueKey: 1818
  - uniqueKey: 1819
  - uniqueKey: 1820
  - uniqueKey: 1821
  - uniqueKey: 1822
  - uniqueKey: 1823
  - uniqueKey: 1824
  - uniqueKey: 1825
  - uniqueKey: 1826
  - uniqueKey: 1827
  - uniqueKey: 1828
  - uniqueKey: 1829
  - uniqueKey: 1830
  - uniqueKey: 1901
  - uniqueKey: 1902
  - uniqueKey: 1903
  - uniqueKey: 1904
  - uniqueKey: 1905
  - uniqueKey: 1906
  - uniqueKey: 1907
  - uniqueKey: 1908
  - uniqueKey: 1909
  - uniqueKey: 1910
  - uniqueKey: 1911
  - uniqueKey: 1912
  - uniqueKey: 1913
  - uniqueKey: 1914
  - uniqueKey: 1915
  - uniqueKey: 1916
  - uniqueKey: 1917
  - uniqueKey: 1918
  - uniqueKey: 1919
  - uniqueKey: 1920
  - uniqueKey: 1921
  - uniqueKey: 1922
  - uniqueKey: 1923
  - uniqueKey: 1924
  - uniqueKey: 1925
  - uniqueKey: 1926
  - uniqueKey: 1927
  - uniqueKey: 1928
  - uniqueKey: 1929
  - uniqueKey: 1930
  - uniqueKey: 2001
  - uniqueKey: 2002
  - uniqueKey: 2003
  - uniqueKey: 2004
  - uniqueKey: 2005
  - uniqueKey: 2006
  - uniqueKey: 2007
  - uniqueKey: 2008
  - uniqueKey: 2009
  - uniqueKey: 2010
  - uniqueKey: 2011
  - uniqueKey: 2012
  - uniqueKey: 2013
  - uniqueKey: 2014
  - uniqueKey: 2015
  - uniqueKey: 2016
  - uniqueKey: 2017
  - uniqueKey: 2018
  - uniqueKey: 2019
  - uniqueKey: 2020
  - uniqueKey: 2021
  - uniqueKey: 2022
  - uniqueKey: 2023
  - uniqueKey: 2024
  - uniqueKey: 2025
  - uniqueKey: 2026
  - uniqueKey: 2027
  - uniqueKey: 2028
  - uniqueKey: 2029
  - uniqueKey: 2030
  - uniqueKey: 2101
  - uniqueKey: 2102
  - uniqueKey: 2103
  - uniqueKey: 2104
  - uniqueKey: 2105
  - uniqueKey: 2106
  - uniqueKey: 2107
  - uniqueKey: 2108
  - uniqueKey: 2109
  - uniqueKey: 2110
  - uniqueKey: 2111
  - uniqueKey: 2112
  - uniqueKey: 2113
  - uniqueKey: 2114
  - uniqueKey: 2115
  - uniqueKey: 2116
  - uniqueKey: 2117
  - uniqueKey: 2118
  - uniqueKey: 2119
  - uniqueKey: 2120
  - uniqueKey: 2121
  - uniqueKey: 2122
  - uniqueKey: 2123
  - uniqueKey: 2124
  - uniqueKey: 2125
  - uniqueKey: 2126
  - uniqueKey: 2127
  - uniqueKey: 2128
  - uniqueKey: 2129
  - uniqueKey: 2130
  - uniqueKey: 2201
  - uniqueKey: 2202
  - uniqueKey: 2203
  - uniqueKey: 2204
  - uniqueKey: 2205
  - uniqueKey: 2206
  - uniqueKey: 2207
  - uniqueKey: 2208
  - uniqueKey: 2209
  - uniqueKey: 2210
  - uniqueKey: 2211
  - uniqueKey: 2212
  - uniqueKey: 2213
  - uniqueKey: 2214
  - uniqueKey: 2215
  - uniqueKey: 2216
  - uniqueKey: 2217
  - uniqueKey: 2218
  - uniqueKey: 2219
  - uniqueKey: 2220
  - uniqueKey: 2221
  - uniqueKey: 2222
  - uniqueKey: 2223
  - uniqueKey: 2224
  - uniqueKey: 2225
  - uniqueKey: 2226
  - uniqueKey: 2227
  - uniqueKey: 2228
  - uniqueKey: 2229
  - uniqueKey: 2230
  - uniqueKey: 2301
  - uniqueKey: 2302
  - uniqueKey: 2303
  - uniqueKey: 2304
  - uniqueKey: 2305
  - uniqueKey: 2306
  - uniqueKey: 2307
  - uniqueKey: 2308
  - uniqueKey: 2309
  - uniqueKey: 2310
  - uniqueKey: 2311
  - uniqueKey: 2312
  - uniqueKey: 2313
  - uniqueKey: 2314
  - uniqueKey: 2315
  - uniqueKey: 2316
  - uniqueKey: 2317
  - uniqueKey: 2318
  - uniqueKey: 2319
  - uniqueKey: 2320
  - uniqueKey: 2321
  - uniqueKey: 2322
  - uniqueKey: 2323
  - uniqueKey: 2324
  - uniqueKey: 2325
  - uniqueKey: 2326
  - uniqueKey: 2327
  - uniqueKey: 2328
  - uniqueKey: 2329
  - uniqueKey: 2330
  - uniqueKey: 2401
  - uniqueKey: 2402
  - uniqueKey: 2403
  - uniqueKey: 2404
  - uniqueKey: 2405
  - uniqueKey: 2406
  - uniqueKey: 2407
  - uniqueKey: 2408
  - uniqueKey: 2409
  - uniqueKey: 2410
  - uniqueKey: 2411
  - uniqueKey: 2412
  - uniqueKey: 2413
  - uniqueKey: 2414
  - uniqueKey: 2415
  - uniqueKey: 2416
  - uniqueKey: 2417
  - uniqueKey: 2418
  - uniqueKey: 2419
  - uniqueKey: 2420
  - uniqueKey: 2421
  - uniqueKey: 2422
  - uniqueKey: 2423
  - uniqueKey: 2424
  - uniqueKey: 2425
  - uniqueKey: 2426
  - uniqueKey: 2427
  - uniqueKey: 2428
  - uniqueKey: 2429
  - uniqueKey: 2430
  datas: []
