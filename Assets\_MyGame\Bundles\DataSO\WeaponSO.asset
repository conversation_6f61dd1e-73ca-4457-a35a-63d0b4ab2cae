%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: WeaponSO
  m_EditorClassIdentifier: Assembly-CSharp::TableSO/WeaponSo
  datas:
  - uniqueKey: 10100
  - uniqueKey: 10101
  - uniqueKey: 10102
  - uniqueKey: 10103
  - uniqueKey: 10104
  - uniqueKey: 10105
  - uniqueKey: 10200
  - uniqueKey: 10201
  - uniqueKey: 10202
  - uniqueKey: 10203
  - uniqueKey: 10204
  - uniqueKey: 10205
  - uniqueKey: 10300
  - uniqueKey: 10301
  - uniqueKey: 10302
  - uniqueKey: 10303
  - uniqueKey: 10304
  - uniqueKey: 10305
  - uniqueKey: 10400
  - uniqueKey: 10401
  - uniqueKey: 10402
  - uniqueKey: 10403
  - uniqueKey: 10404
  - uniqueKey: 10405
  - uniqueKey: 10500
  - uniqueKey: 10501
  - uniqueKey: 10502
  - uniqueKey: 10503
  - uniqueKey: 10504
  - uniqueKey: 10505
  - uniqueKey: 10600
  - uniqueKey: 10601
  - uniqueKey: 10602
  - uniqueKey: 10603
  - uniqueKey: 10604
  - uniqueKey: 10605
  - uniqueKey: 10700
  - uniqueKey: 10701
  - uniqueKey: 10702
  - uniqueKey: 10703
  - uniqueKey: 10704
  - uniqueKey: 10705
  - uniqueKey: 10800
  - uniqueKey: 10801
  - uniqueKey: 10802
  - uniqueKey: 10803
  - uniqueKey: 10804
  - uniqueKey: 10805
  - uniqueKey: 10900
  - uniqueKey: 10901
  - uniqueKey: 10902
  - uniqueKey: 10903
  - uniqueKey: 10904
  - uniqueKey: 10905
  - uniqueKey: 11000
  - uniqueKey: 11001
  - uniqueKey: 11002
  - uniqueKey: 11003
  - uniqueKey: 11004
  - uniqueKey: 11005
  - uniqueKey: 11100
  - uniqueKey: 11101
  - uniqueKey: 11102
  - uniqueKey: 11103
  - uniqueKey: 11104
  - uniqueKey: 11105
  - uniqueKey: 11200
  - uniqueKey: 11201
  - uniqueKey: 11202
  - uniqueKey: 11203
  - uniqueKey: 11204
  - uniqueKey: 11205
  - uniqueKey: 11300
  - uniqueKey: 11301
  - uniqueKey: 11302
  - uniqueKey: 11303
  - uniqueKey: 11304
  - uniqueKey: 11305
  - uniqueKey: 11400
  - uniqueKey: 11401
  - uniqueKey: 11402
  - uniqueKey: 11403
  - uniqueKey: 11404
  - uniqueKey: 11405
  - uniqueKey: 11500
  - uniqueKey: 11501
  - uniqueKey: 11502
  - uniqueKey: 11503
  - uniqueKey: 11504
  - uniqueKey: 11505
  - uniqueKey: 11600
  - uniqueKey: 11601
  - uniqueKey: 11602
  - uniqueKey: 11603
  - uniqueKey: 11604
  - uniqueKey: 11605
  - uniqueKey: 11700
  - uniqueKey: 11701
  - uniqueKey: 11702
  - uniqueKey: 11703
  - uniqueKey: 11704
  - uniqueKey: 11705
  - uniqueKey: 11800
  - uniqueKey: 11801
  - uniqueKey: 11802
  - uniqueKey: 11803
  - uniqueKey: 11804
  - uniqueKey: 11805
  - uniqueKey: 11900
  - uniqueKey: 11901
  - uniqueKey: 11902
  - uniqueKey: 11903
  - uniqueKey: 11904
  - uniqueKey: 11905
  - uniqueKey: 12000
  - uniqueKey: 12001
  - uniqueKey: 12002
  - uniqueKey: 12003
  - uniqueKey: 12004
  - uniqueKey: 12005
  - uniqueKey: 12100
  - uniqueKey: 12101
  - uniqueKey: 12102
  - uniqueKey: 12103
  - uniqueKey: 12104
  - uniqueKey: 12105
  - uniqueKey: 12200
  - uniqueKey: 12201
  - uniqueKey: 12202
  - uniqueKey: 12203
  - uniqueKey: 12204
  - uniqueKey: 12205
  - uniqueKey: 12300
  - uniqueKey: 12301
  - uniqueKey: 12302
  - uniqueKey: 12303
  - uniqueKey: 12304
  - uniqueKey: 12305
  - uniqueKey: 12400
  - uniqueKey: 12401
  - uniqueKey: 12402
  - uniqueKey: 12403
  - uniqueKey: 12404
  - uniqueKey: 12405
  - uniqueKey: 12500
  - uniqueKey: 12501
  - uniqueKey: 12502
  - uniqueKey: 12503
  - uniqueKey: 12504
  - uniqueKey: 12505
  - uniqueKey: 12600
  - uniqueKey: 12601
  - uniqueKey: 12602
  - uniqueKey: 12603
  - uniqueKey: 12604
  - uniqueKey: 12605
  - uniqueKey: 12700
  - uniqueKey: 12701
  - uniqueKey: 12702
  - uniqueKey: 12703
  - uniqueKey: 12704
  - uniqueKey: 12705
  - uniqueKey: 12800
  - uniqueKey: 12801
  - uniqueKey: 12802
  - uniqueKey: 12803
  - uniqueKey: 12804
  - uniqueKey: 12805
  - uniqueKey: 12900
  - uniqueKey: 12901
  - uniqueKey: 12902
  - uniqueKey: 12903
  - uniqueKey: 12904
  - uniqueKey: 12905
  - uniqueKey: 13000
  - uniqueKey: 13001
  - uniqueKey: 13002
  - uniqueKey: 13003
  - uniqueKey: 13004
  - uniqueKey: 13005
  - uniqueKey: 13100
  - uniqueKey: 13101
  - uniqueKey: 13102
  - uniqueKey: 13103
  - uniqueKey: 13104
  - uniqueKey: 13105
  - uniqueKey: 13200
  - uniqueKey: 13201
  - uniqueKey: 13202
  - uniqueKey: 13203
  - uniqueKey: 13204
  - uniqueKey: 13205
  - uniqueKey: 13300
  - uniqueKey: 13301
  - uniqueKey: 13302
  - uniqueKey: 13303
  - uniqueKey: 13304
  - uniqueKey: 13305
  - uniqueKey: 13400
  - uniqueKey: 13401
  - uniqueKey: 13402
  - uniqueKey: 13403
  - uniqueKey: 13404
  - uniqueKey: 13405
  - uniqueKey: 13500
  - uniqueKey: 13501
  - uniqueKey: 13502
  - uniqueKey: 13503
  - uniqueKey: 13504
  - uniqueKey: 13505
  - uniqueKey: 13600
  - uniqueKey: 13601
  - uniqueKey: 13602
  - uniqueKey: 13603
  - uniqueKey: 13604
  - uniqueKey: 13605
  - uniqueKey: 13700
  - uniqueKey: 13701
  - uniqueKey: 13702
  - uniqueKey: 13703
  - uniqueKey: 13704
  - uniqueKey: 13705
  - uniqueKey: 13800
  - uniqueKey: 13801
  - uniqueKey: 13802
  - uniqueKey: 13803
  - uniqueKey: 13804
  - uniqueKey: 13805
  - uniqueKey: 13900
  - uniqueKey: 13901
  - uniqueKey: 13902
  - uniqueKey: 13903
  - uniqueKey: 13904
  - uniqueKey: 13905
  - uniqueKey: 14000
  - uniqueKey: 14001
  - uniqueKey: 14002
  - uniqueKey: 14003
  - uniqueKey: 14004
  - uniqueKey: 14005
  - uniqueKey: 14100
  - uniqueKey: 14101
  - uniqueKey: 14102
  - uniqueKey: 14103
  - uniqueKey: 14104
  - uniqueKey: 14105
  - uniqueKey: 1100000
  - uniqueKey: 1100001
  - uniqueKey: 1100002
  - uniqueKey: 1100003
  - uniqueKey: 1100004
  - uniqueKey: 1100005
  - uniqueKey: 1100010
  - uniqueKey: 1100011
  - uniqueKey: 1100012
  - uniqueKey: 1100013
  - uniqueKey: 1100014
  - uniqueKey: 1100015
  - uniqueKey: 1101000
  - uniqueKey: 1101001
  - uniqueKey: 1101002
  - uniqueKey: 1101003
  - uniqueKey: 1101004
  - uniqueKey: 1101005
  - uniqueKey: 1101010
  - uniqueKey: 1101011
  - uniqueKey: 1101012
  - uniqueKey: 1101013
  - uniqueKey: 1101014
  - uniqueKey: 1101015
  - uniqueKey: 1102000
  - uniqueKey: 1102001
  - uniqueKey: 1102002
  - uniqueKey: 1102003
  - uniqueKey: 1102004
  - uniqueKey: 1102005
  - uniqueKey: 1102010
  - uniqueKey: 1102011
  - uniqueKey: 1102012
  - uniqueKey: 1102013
  - uniqueKey: 1102014
  - uniqueKey: 1102015
  - uniqueKey: 1103000
  - uniqueKey: 1103001
  - uniqueKey: 1103002
  - uniqueKey: 1103003
  - uniqueKey: 1103004
  - uniqueKey: 1103005
  - uniqueKey: 1103010
  - uniqueKey: 1103011
  - uniqueKey: 1103012
  - uniqueKey: 1103013
  - uniqueKey: 1103014
  - uniqueKey: 1103015
  - uniqueKey: 1104000
  - uniqueKey: 1104001
  - uniqueKey: 1104002
  - uniqueKey: 1104003
  - uniqueKey: 1104004
  - uniqueKey: 1104005
  - uniqueKey: 1104010
  - uniqueKey: 1104011
  - uniqueKey: 1104012
  - uniqueKey: 1104013
  - uniqueKey: 1104014
  - uniqueKey: 1104015
  - uniqueKey: 1105000
  - uniqueKey: 1105001
  - uniqueKey: 1105002
  - uniqueKey: 1105003
  - uniqueKey: 1105004
  - uniqueKey: 1105005
  - uniqueKey: 1105010
  - uniqueKey: 1105011
  - uniqueKey: 1105012
  - uniqueKey: 1105013
  - uniqueKey: 1105014
  - uniqueKey: 1105015
  - uniqueKey: 1106000
  - uniqueKey: 1106001
  - uniqueKey: 1106002
  - uniqueKey: 1106003
  - uniqueKey: 1106004
  - uniqueKey: 1106005
  - uniqueKey: 1106010
  - uniqueKey: 1106011
  - uniqueKey: 1106012
  - uniqueKey: 1106013
  - uniqueKey: 1106014
  - uniqueKey: 1106015
  - uniqueKey: 1107000
  - uniqueKey: 1107001
  - uniqueKey: 1107002
  - uniqueKey: 1107003
  - uniqueKey: 1107004
  - uniqueKey: 1107005
  - uniqueKey: 1107010
  - uniqueKey: 1107011
  - uniqueKey: 1107012
  - uniqueKey: 1107013
  - uniqueKey: 1107014
  - uniqueKey: 1107015
  - uniqueKey: 1108000
  - uniqueKey: 1108001
  - uniqueKey: 1108002
  - uniqueKey: 1108003
  - uniqueKey: 1108004
  - uniqueKey: 1108005
  - uniqueKey: 1108010
  - uniqueKey: 1108011
  - uniqueKey: 1108012
  - uniqueKey: 1108013
  - uniqueKey: 1108014
  - uniqueKey: 1108015
  - uniqueKey: 1109000
  - uniqueKey: 1109001
  - uniqueKey: 1109002
  - uniqueKey: 1109003
  - uniqueKey: 1109004
  - uniqueKey: 1109005
  - uniqueKey: 1109010
  - uniqueKey: 1109011
  - uniqueKey: 1109012
  - uniqueKey: 1109013
  - uniqueKey: 1109014
  - uniqueKey: 1109015
  - uniqueKey: 1110000
  - uniqueKey: 1110001
  - uniqueKey: 1110002
  - uniqueKey: 1110003
  - uniqueKey: 1110004
  - uniqueKey: 1110005
  - uniqueKey: 1110010
  - uniqueKey: 1110011
  - uniqueKey: 1110012
  - uniqueKey: 1110013
  - uniqueKey: 1110014
  - uniqueKey: 1110015
  - uniqueKey: 1111000
  - uniqueKey: 1111001
  - uniqueKey: 1111002
  - uniqueKey: 1111003
  - uniqueKey: 1111004
  - uniqueKey: 1111005
  - uniqueKey: 1111010
  - uniqueKey: 1111011
  - uniqueKey: 1111012
  - uniqueKey: 1111013
  - uniqueKey: 1111014
  - uniqueKey: 1111015
  - uniqueKey: 20000
  - uniqueKey: 20001
  - uniqueKey: 20002
  - uniqueKey: 20003
  - uniqueKey: 20004
  - uniqueKey: 21000
  - uniqueKey: 21001
  - uniqueKey: 21002
  - uniqueKey: 21003
  - uniqueKey: 21004
  - uniqueKey: 22000
  - uniqueKey: 22001
  - uniqueKey: 22002
  - uniqueKey: 22003
  - uniqueKey: 22004
  - uniqueKey: 22005
  - uniqueKey: 22006
  - uniqueKey: 22007
  - uniqueKey: 22008
  - uniqueKey: 22009
  - uniqueKey: 22100
  - uniqueKey: 22101
  - uniqueKey: 22102
  - uniqueKey: 22103
  - uniqueKey: 22104
  - uniqueKey: 22105
  - uniqueKey: 22106
  - uniqueKey: 22107
  - uniqueKey: 22108
  - uniqueKey: 22109
  - uniqueKey: 22200
  - uniqueKey: 22201
  - uniqueKey: 22202
  - uniqueKey: 22203
  - uniqueKey: 22204
  - uniqueKey: 22205
  - uniqueKey: 22206
  - uniqueKey: 22207
  - uniqueKey: 22208
  - uniqueKey: 22209
  - uniqueKey: 30100
  - uniqueKey: 30101
  - uniqueKey: 30102
  - uniqueKey: 30103
  - uniqueKey: 30104
  - uniqueKey: 30105
  - uniqueKey: 30106
  - uniqueKey: 30107
  - uniqueKey: 30108
  - uniqueKey: 30109
  - uniqueKey: 30200
  - uniqueKey: 30201
  - uniqueKey: 30202
  - uniqueKey: 30203
  - uniqueKey: 30204
  - uniqueKey: 30205
  - uniqueKey: 30206
  - uniqueKey: 30207
  - uniqueKey: 30208
  - uniqueKey: 30209
  - uniqueKey: 30300
  - uniqueKey: 30301
  - uniqueKey: 30302
  - uniqueKey: 30303
  - uniqueKey: 30304
  - uniqueKey: 30305
  - uniqueKey: 30306
  - uniqueKey: 30307
  - uniqueKey: 30308
  - uniqueKey: 30309
  - uniqueKey: 30400
  - uniqueKey: 30401
  - uniqueKey: 30402
  - uniqueKey: 30403
  - uniqueKey: 30404
  - uniqueKey: 30405
  - uniqueKey: 30406
  - uniqueKey: 30407
  - uniqueKey: 30408
  - uniqueKey: 30409
  - uniqueKey: 30500
  - uniqueKey: 30501
  - uniqueKey: 30502
  - uniqueKey: 30503
  - uniqueKey: 30504
  - uniqueKey: 30505
  - uniqueKey: 30506
  - uniqueKey: 30507
  - uniqueKey: 30508
  - uniqueKey: 30509
  - uniqueKey: 40100
  - uniqueKey: 40101
  - uniqueKey: 40102
  - uniqueKey: 40103
  - uniqueKey: 40104
  - uniqueKey: 40105
  - uniqueKey: 40106
  - uniqueKey: 40107
  - uniqueKey: 40108
  - uniqueKey: 40109
  - uniqueKey: 40200
  - uniqueKey: 40201
  - uniqueKey: 40202
  - uniqueKey: 40203
  - uniqueKey: 40204
  - uniqueKey: 40205
  - uniqueKey: 40206
  - uniqueKey: 40207
  - uniqueKey: 40208
  - uniqueKey: 40209
  - uniqueKey: 40300
  - uniqueKey: 40301
  - uniqueKey: 40302
  - uniqueKey: 40303
  - uniqueKey: 40304
  - uniqueKey: 40305
  - uniqueKey: 40306
  - uniqueKey: 40307
  - uniqueKey: 40308
  - uniqueKey: 40309
  - uniqueKey: 40400
  - uniqueKey: 40401
  - uniqueKey: 40402
  - uniqueKey: 40403
  - uniqueKey: 40404
  - uniqueKey: 40405
  - uniqueKey: 40406
  - uniqueKey: 40407
  - uniqueKey: 40408
  - uniqueKey: 40409
  - uniqueKey: 82000
  - uniqueKey: 82001
  - uniqueKey: 82002
  - uniqueKey: 82003
  - uniqueKey: 82004
  - uniqueKey: 82005
  - uniqueKey: 60000
  - uniqueKey: 60001
  - uniqueKey: 60002
  - uniqueKey: 61000
  - uniqueKey: 61001
  - uniqueKey: 61101
  - uniqueKey: 61102
  - uniqueKey: 61103
  - uniqueKey: 62000
  - uniqueKey: 62001
  - uniqueKey: 62002
  - uniqueKey: 62003
  - uniqueKey: 62004
  - uniqueKey: 62005
  - uniqueKey: 62100
  - uniqueKey: 62101
  - uniqueKey: 62102
  - uniqueKey: 63000
  - uniqueKey: 63001
  - uniqueKey: 63002
  - uniqueKey: 63003
  - uniqueKey: 63004
  - uniqueKey: 63005
  - uniqueKey: 63100
  - uniqueKey: 63101
  - uniqueKey: 63102
  - uniqueKey: 64000
  - uniqueKey: 64001
  - uniqueKey: 64002
  - uniqueKey: 64003
  - uniqueKey: 64100
  - uniqueKey: 65000
  - uniqueKey: 65001
  - uniqueKey: 65002
  - uniqueKey: 65003
  - uniqueKey: 65100
  - uniqueKey: 66000
  - uniqueKey: 66001
  - uniqueKey: 66100
  - uniqueKey: 67000
  - uniqueKey: 67001
  - uniqueKey: 67002
  - uniqueKey: 67003
  - uniqueKey: 67004
  - uniqueKey: 67005
  - uniqueKey: 67100
  - uniqueKey: 67101
  - uniqueKey: 67102
  - uniqueKey: 68000
  - uniqueKey: 68001
  - uniqueKey: 68002
  - uniqueKey: 68003
  - uniqueKey: 68004
  - uniqueKey: 68005
  - uniqueKey: 68100
  - uniqueKey: 68101
  - uniqueKey: 68102
  - uniqueKey: 69000
  - uniqueKey: 69001
  - uniqueKey: 69002
  - uniqueKey: 69010
  - uniqueKey: 69100
  - uniqueKey: 69101
  - uniqueKey: 69102
  - uniqueKey: 69110
  - uniqueKey: 70000
  - uniqueKey: 70010
  - uniqueKey: 70011
  - uniqueKey: 70012
  - uniqueKey: 70101
  - uniqueKey: 70102
  - uniqueKey: 70103
  - uniqueKey: 71000
  - uniqueKey: 71010
  - uniqueKey: 71011
  - uniqueKey: 71012
  - uniqueKey: 71100
  - uniqueKey: 71101
  - uniqueKey: 71102
  - uniqueKey: 72000
  - uniqueKey: 72010
  - uniqueKey: 72011
  - uniqueKey: 72012
  - uniqueKey: 72100
  - uniqueKey: 72101
  - uniqueKey: 72102
  - uniqueKey: 73000
  - uniqueKey: 73001
  - uniqueKey: 73002
  - uniqueKey: 73010
  - uniqueKey: 73100
  - uniqueKey: 73101
  - uniqueKey: 73102
  - uniqueKey: 74000
  - uniqueKey: 74001
  - uniqueKey: 74002
  - uniqueKey: 74010
  - uniqueKey: 74011
  - uniqueKey: 74012
  - uniqueKey: 74100
  - uniqueKey: 74101
  - uniqueKey: 74102
  datas: []
